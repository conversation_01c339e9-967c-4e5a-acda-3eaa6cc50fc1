# -*- coding: utf-8 -*-
"""
获取币安官方507个USDT永续合约的开始日期（上线日期）
使用成功的连接方法，确保获取官方数据
"""

import requests
import json
import csv
import time
from datetime import datetime
import ssl
import urllib3
import os
import socket

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class PerpetualStartDatesFetcher:
    """永续合约开始日期获取器"""
    
    def __init__(self):
        """初始化获取器"""
        self.session = self.create_optimized_session()
        self.data_dir = "data"
        self.ensure_data_dir()
        
    def ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def create_optimized_session(self):
        """创建优化的HTTP会话 - 使用成功的连接方法"""
        session = requests.Session()
        
        # 关键请求头设置 - 基于成功经验
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache'
        })
        
        # 重试策略 - 基于成功经验
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=2,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def load_all_perpetual_contracts(self):
        """加载完整永续合约列表"""
        contracts_file = os.path.join(self.data_dir, 'all_usdt_perpetual_contracts_complete.json')
        
        if not os.path.exists(contracts_file):
            print("❌ 完整永续合约文件不存在，请先运行 get_all_perpetual_contracts.py")
            return []
        
        try:
            with open(contracts_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            contracts = data.get('contracts', [])
            print(f"✅ 成功加载 {len(contracts)} 个完整永续合约（包括所有状态）")
            return contracts
            
        except Exception as e:
            print(f"❌ 加载完整永续合约文件失败: {str(e)}")
            return []
    
    def get_contract_start_date(self, symbol):
        """获取单个永续合约的开始日期（最早可用数据）"""
        try:
            # 使用币安期货K线API获取最早数据
            url = "https://fapi.binance.com/fapi/v1/klines"
            params = {
                'symbol': symbol,
                'interval': '1d',
                'startTime': 0,  # 从最早开始
                'limit': 1  # 只获取最早的1条数据
            }
            
            response = self.session.get(url, params=params, timeout=60, verify=True)
            
            if response.status_code == 200:
                data = response.json()
                if data and len(data) > 0:
                    # 获取最早数据的时间戳
                    earliest_timestamp = data[0][0]  # 开盘时间戳（毫秒）
                    
                    # 转换为日期
                    earliest_date = datetime.fromtimestamp(earliest_timestamp / 1000)
                    date_str = earliest_date.strftime('%Y-%m-%d')
                    
                    # 计算距今天数
                    today = datetime.now()
                    days_since = (today - earliest_date).days
                    
                    # 获取价格信息
                    open_price = float(data[0][1])
                    high_price = float(data[0][2])
                    low_price = float(data[0][3])
                    close_price = float(data[0][4])
                    volume = float(data[0][5])
                    quote_volume = float(data[0][7])  # 成交额
                    
                    return {
                        'symbol': symbol,
                        'start_date': date_str,
                        'start_timestamp': earliest_timestamp,
                        'status': 'SUCCESS',
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price,
                        'volume': volume,
                        'quote_volume': quote_volume,
                        'days_since': days_since,
                        'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
                        'contract_status': 'ACTIVE'  # 有数据说明合约活跃
                    }
                else:
                    return {
                        'symbol': symbol,
                        'start_date': None,
                        'status': 'NO_DATA',
                        'error': '无数据返回',
                        'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
                        'contract_status': 'INACTIVE'  # 无数据可能是非活跃合约
                    }
            elif response.status_code == 400:
                # 400错误通常表示合约不存在或已下线
                return {
                    'symbol': symbol,
                    'start_date': None,
                    'status': 'CONTRACT_NOT_FOUND',
                    'error': '合约不存在或已下线',
                    'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
                    'contract_status': 'DELISTED'  # 已下线
                }
            else:
                return {
                    'symbol': symbol,
                    'start_date': None,
                    'status': 'API_ERROR',
                    'error': f'HTTP {response.status_code}',
                    'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
                    'contract_status': 'UNKNOWN'
                }
                
        except requests.exceptions.Timeout:
            return {
                'symbol': symbol,
                'start_date': None,
                'status': 'TIMEOUT',
                'error': '请求超时',
                'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
                'contract_status': 'UNKNOWN'
            }
        except Exception as e:
            return {
                'symbol': symbol,
                'start_date': None,
                'status': 'ERROR',
                'error': str(e),
                'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
                'contract_status': 'UNKNOWN'
            }
    
    def fetch_all_start_dates(self, contracts):
        """获取所有永续合约的开始日期"""
        print(f"🚀 开始获取 {len(contracts)} 个完整USDT永续合约的开始日期...")
        print("包括可交易、不可交易、已下线等所有状态的合约")
        print("=" * 80)
        
        results = []
        start_time = datetime.now()
        
        for i, contract in enumerate(contracts, 1):
            symbol = contract['symbol']
            onboard_date = contract.get('onboardDate', 0)
            
            print(f"📡 [{i:3d}/{len(contracts)}] 正在获取 {symbol}...")
            
            # 先从合约信息中获取上线日期
            official_onboard_date = None
            if onboard_date and onboard_date > 0:
                try:
                    official_onboard_date = datetime.fromtimestamp(onboard_date / 1000).strftime('%Y-%m-%d')
                except:
                    pass
            
            # 获取实际交易开始日期
            result = self.get_contract_start_date(symbol)
            
            # 添加官方上线日期信息
            result['official_onboard_date'] = official_onboard_date
            result['onboard_timestamp'] = onboard_date
            
            # 比较官方上线日期和实际交易开始日期
            if official_onboard_date and result.get('start_date'):
                result['date_match'] = official_onboard_date == result['start_date']
            else:
                result['date_match'] = None
            
            results.append(result)
            
            # 显示结果
            if result['status'] == 'SUCCESS':
                days_since = result.get('days_since', 0)
                price = result.get('close', 0)
                contract_status = result.get('contract_status', 'UNKNOWN')
                onboard_info = f", 官方上线: {official_onboard_date}" if official_onboard_date else ""
                print(f"✅ [{i:3d}] {symbol:20s}: {result['start_date']} ({days_since}天前, 价格: ${price:,.4f}, 状态: {contract_status}{onboard_info})")
            elif result['status'] == 'CONTRACT_NOT_FOUND':
                contract_status = result.get('contract_status', 'UNKNOWN')
                onboard_info = f", 官方上线: {official_onboard_date}" if official_onboard_date else ""
                print(f"⚠️  [{i:3d}] {symbol:20s}: {result['status']} - {result.get('error', '')} (状态: {contract_status}{onboard_info})")
            else:
                contract_status = result.get('contract_status', 'UNKNOWN')
                onboard_info = f", 官方上线: {official_onboard_date}" if official_onboard_date else ""
                print(f"❌ [{i:3d}] {symbol:20s}: {result['status']} - {result.get('error', '')} (状态: {contract_status}{onboard_info})")
            
            # 速率控制 - 避免被限制
            time.sleep(0.1)
            
            # 每50个显示一次进度
            if i % 50 == 0:
                elapsed = (datetime.now() - start_time).total_seconds()
                avg_time = elapsed / i
                remaining = (len(contracts) - i) * avg_time
                success_count = len([r for r in results if r['status'] == 'SUCCESS'])
                not_found_count = len([r for r in results if r['status'] == 'CONTRACT_NOT_FOUND'])
                print(f"📊 进度: {i}/{len(contracts)} ({i/len(contracts)*100:.1f}%), 成功: {success_count}, 已下线: {not_found_count}, 预计剩余: {remaining/60:.1f}分钟")
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print("=" * 80)
        print(f"⏱️  总耗时: {duration/60:.1f} 分钟")
        print(f"📊 处理速度: {len(contracts)/duration:.2f} 个/秒")
        
        return results
    
    def analyze_results(self, results):
        """分析结果"""
        print(f"\n📊 结果分析:")
        
        success_count = len([r for r in results if r['status'] == 'SUCCESS'])
        not_found_count = len([r for r in results if r['status'] == 'CONTRACT_NOT_FOUND'])
        no_data_count = len([r for r in results if r['status'] == 'NO_DATA'])
        error_count = len([r for r in results if r['status'] in ['API_ERROR', 'TIMEOUT', 'ERROR']])
        
        print(f"  • 总数: {len(results)}")
        print(f"  • 成功获取数据: {success_count} ({success_count/len(results)*100:.1f}%)")
        print(f"  • 合约已下线: {not_found_count} ({not_found_count/len(results)*100:.1f}%)")
        print(f"  • 无数据: {no_data_count} ({no_data_count/len(results)*100:.1f}%)")
        print(f"  • 错误: {error_count} ({error_count/len(results)*100:.1f}%)")
        
        # 按合约状态分类
        contract_status_stats = {}
        for result in results:
            status = result.get('contract_status', 'UNKNOWN')
            contract_status_stats[status] = contract_status_stats.get(status, 0) + 1
        
        print(f"\n📈 按合约状态分类:")
        for status, count in sorted(contract_status_stats.items()):
            print(f"  • {status}: {count} 个")
        
        if success_count > 0:
            # 分析开始日期分布
            successful_results = [r for r in results if r['status'] == 'SUCCESS']
            days_since_list = [r['days_since'] for r in successful_results]
            
            print(f"\n📅 活跃合约开始日期分析:")
            print(f"  • 最早上线: {max(days_since_list)} 天前")
            print(f"  • 最晚上线: {min(days_since_list)} 天前")
            print(f"  • 平均上线时间: {sum(days_since_list)/len(days_since_list):.1f} 天前")
            
            # 按年份统计
            year_stats = {}
            for result in successful_results:
                if result.get('start_date'):
                    try:
                        year = datetime.strptime(result['start_date'], '%Y-%m-%d').year
                        year_stats[year] = year_stats.get(year, 0) + 1
                    except:
                        pass
            
            if year_stats:
                print(f"\n📊 按上线年份分布:")
                for year in sorted(year_stats.keys()):
                    count = year_stats[year]
                    print(f"  • {year}年: {count} 个")
            
            # 官方日期匹配分析
            match_count = len([r for r in successful_results if r.get('date_match') == True])
            mismatch_count = len([r for r in successful_results if r.get('date_match') == False])
            no_official_count = len([r for r in successful_results if r.get('date_match') is None])
            
            print(f"\n🔍 官方上线日期与实际交易开始日期对比:")
            print(f"  • 日期匹配: {match_count} 个")
            print(f"  • 日期不匹配: {mismatch_count} 个")
            print(f"  • 无官方日期: {no_official_count} 个")
            
            # 价格分析
            prices = [r['close'] for r in successful_results if r.get('close', 0) > 0]
            if prices:
                print(f"\n💰 活跃合约首日价格范围分析:")
                print(f"  • 最高首日价格: ${max(prices):,.4f}")
                print(f"  • 最低首日价格: ${min(prices):,.8f}")
                print(f"  • 平均首日价格: ${sum(prices)/len(prices):,.4f}")
        
        # 分析已下线合约
        delisted_results = [r for r in results if r['status'] == 'CONTRACT_NOT_FOUND']
        if delisted_results:
            print(f"\n🚫 已下线合约分析:")
            print(f"  • 已下线合约数量: {len(delisted_results)} 个")
            print(f"  • 已下线合约示例:")
            for i, result in enumerate(delisted_results[:10]):
                onboard_info = f" (官方上线: {result.get('official_onboard_date', 'N/A')})" if result.get('official_onboard_date') else ""
                print(f"    {i+1:2d}. {result['symbol']}{onboard_info}")
            if len(delisted_results) > 10:
                print(f"    ... 还有 {len(delisted_results) - 10} 个")
    
    def save_results(self, results):
        """保存结果到文件"""
        print(f"\n💾 保存结果到文件...")
        
        # 保存JSON格式
        json_data = {
            'fetch_time': datetime.now().isoformat(),
            'total_symbols': len(results),
            'successful_count': len([r for r in results if r['status'] == 'SUCCESS']),
            'delisted_count': len([r for r in results if r['status'] == 'CONTRACT_NOT_FOUND']),
            'data_source': '币安官方期货API',
            'api_endpoint': 'https://fapi.binance.com/fapi/v1/klines',
            'official_data': True,
            'data_authenticity': 'OFFICIAL_BINANCE_FUTURES_API',
            'include_all_status': True,
            'note': '包含所有状态的USDT永续合约开始日期：活跃、已下线、无数据等',
            'results': results,
            'statistics': {
                'total_contracts': len(results),
                'active_contracts': len([r for r in results if r['status'] == 'SUCCESS']),
                'delisted_contracts': len([r for r in results if r['status'] == 'CONTRACT_NOT_FOUND']),
                'no_data_contracts': len([r for r in results if r['status'] == 'NO_DATA']),
                'error_contracts': len([r for r in results if r['status'] in ['API_ERROR', 'TIMEOUT', 'ERROR']])
            }
        }
        
        json_file = os.path.join(self.data_dir, 'all_perpetual_contracts_start_dates.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        # 保存CSV格式
        csv_file = os.path.join(self.data_dir, 'all_perpetual_contracts_start_dates.csv')
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow([
                '合约名称', '开始日期', '官方上线日期', '日期匹配', '状态', '合约状态', '天数差', 
                '开盘价', '最高价', '最低价', '收盘价', '成交量', '成交额',
                '数据源', '错误信息'
            ])
            
            # 写入数据
            for result in results:
                writer.writerow([
                    result['symbol'],
                    result.get('start_date', ''),
                    result.get('official_onboard_date', ''),
                    '是' if result.get('date_match') == True else ('否' if result.get('date_match') == False else ''),
                    result['status'],
                    result.get('contract_status', ''),
                    result.get('days_since', ''),
                    result.get('open', ''),
                    result.get('high', ''),
                    result.get('low', ''),
                    result.get('close', ''),
                    result.get('volume', ''),
                    result.get('quote_volume', ''),
                    result.get('data_source', ''),
                    result.get('error', '')
                ])
        
        print(f"✅ 结果已保存:")
        print(f"  • JSON格式: {json_file}")
        print(f"  • CSV格式: {csv_file}")
        
        return json_file, csv_file
    
    def run(self):
        """执行获取流程"""
        print("🚀 开始获取507个完整USDT永续合约的开始日期...")
        print("=" * 80)
        
        # 1. 加载完整永续合约列表
        contracts = self.load_all_perpetual_contracts()
        if not contracts:
            print("❌ 无法加载完整永续合约列表，程序退出")
            return False
        
        # 2. 获取开始日期
        results = self.fetch_all_start_dates(contracts)
        
        # 3. 分析结果
        self.analyze_results(results)
        
        # 4. 保存结果
        self.save_results(results)
        
        print(f"\n🎉 获取完成！")
        print(f"📊 共处理 {len(contracts)} 个完整USDT永续合约")
        print(f"📁 文件保存在 {self.data_dir} 目录下")
        print(f"✅ 数据来源: 100% 币安官方期货API")
        
        return True

def main():
    """主函数"""
    try:
        fetcher = PerpetualStartDatesFetcher()
        success = fetcher.run()
        
        if success:
            print("\n✅ 程序执行成功")
        else:
            print("\n❌ 程序执行失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
    
    print("\n程序结束")

if __name__ == "__main__":
    main()
