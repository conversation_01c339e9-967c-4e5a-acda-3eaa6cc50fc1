# -*- coding: utf-8 -*-
"""
分析507个USDT永续合约简单策略回测结果
"""

import pandas as pd
import json
import os

def analyze_backtest_results():
    """分析回测结果"""
    
    # 读取配置文件
    config_file = "data/backtest_results/backtest_config.json"
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 读取汇总结果
    summary_file = "data/backtest_results/backtest_summary.csv"
    df = pd.read_csv(summary_file)
    
    print("🎉 507个USDT永续合约简单策略回测结果分析")
    print("=" * 80)
    
    # 策略信息
    print(f"📈 策略信息:")
    print(f"  • 策略名称: {config['strategy_name']}")
    print(f"  • 短期均线: MA{config['short_ma']}")
    print(f"  • 长期均线: MA{config['long_ma']}")
    print(f"  • 初始资金: ${config['initial_capital']:,}")
    print(f"  • 仓位比例: {config['position_size']*100:.1f}%")
    print(f"  • 交易费用: {config['transaction_cost']*100:.1f}%")
    print(f"  • 回测时间: {config['backtest_time'][:19]}")
    
    # 基本统计
    print(f"\n📊 基本统计:")
    print(f"  • 总合约数: {len(df)}")
    print(f"  • 成功回测: {len(df)} (100%)")
    
    # 收益率统计
    print(f"\n💰 收益率统计:")
    print(f"  • 策略平均收益率: {df['strategy_return'].mean():8.2%}")
    print(f"  • 策略收益率中位数: {df['strategy_return'].median():8.2%}")
    print(f"  • 策略收益率标准差: {df['strategy_return'].std():8.2%}")
    print(f"  • 买入持有平均收益率: {df['buy_hold_return'].mean():8.2%}")
    print(f"  • 平均超额收益: {df['excess_return'].mean():8.2%}")
    print(f"  • 超额收益中位数: {df['excess_return'].median():8.2%}")
    
    # 胜率统计
    positive_returns = len(df[df['strategy_return'] > 0])
    positive_excess = len(df[df['excess_return'] > 0])
    print(f"\n🎯 胜率统计:")
    print(f"  • 盈利合约数: {positive_returns} / {len(df)} ({positive_returns/len(df)*100:.1f}%)")
    print(f"  • 跑赢基准合约数: {positive_excess} / {len(df)} ({positive_excess/len(df)*100:.1f}%)")
    print(f"  • 平均交易胜率: {df['win_rate'].mean():6.1%}")
    print(f"  • 交易胜率中位数: {df['win_rate'].median():6.1%}")
    
    # 风险指标
    print(f"\n⚠️ 风险指标:")
    print(f"  • 平均最大回撤: {df['max_drawdown'].mean():8.2%}")
    print(f"  • 最大回撤中位数: {df['max_drawdown'].median():8.2%}")
    print(f"  • 最大回撤标准差: {df['max_drawdown'].std():8.2%}")
    print(f"  • 平均夏普比率: {df['sharpe_ratio'].mean():8.2f}")
    print(f"  • 夏普比率中位数: {df['sharpe_ratio'].median():8.2f}")
    
    # 交易统计
    print(f"\n📊 交易统计:")
    print(f"  • 平均交易次数: {df['total_trades'].mean():6.1f}")
    print(f"  • 交易次数中位数: {df['total_trades'].median():6.0f}")
    print(f"  • 平均回测天数: {df['total_days'].mean():6.0f}")
    print(f"  • 回测天数中位数: {df['total_days'].median():6.0f}")
    
    # 收益率分布
    print(f"\n📈 策略收益率分布:")
    print(f"  • 最高收益率: {df['strategy_return'].max():8.2%}")
    print(f"  • 最低收益率: {df['strategy_return'].min():8.2%}")
    print(f"  • 收益率 > 50%: {len(df[df['strategy_return'] > 0.5])} 个")
    print(f"  • 收益率 > 20%: {len(df[df['strategy_return'] > 0.2])} 个")
    print(f"  • 收益率 > 0%: {len(df[df['strategy_return'] > 0])} 个")
    print(f"  • 收益率 < -20%: {len(df[df['strategy_return'] < -0.2])} 个")
    
    # 超额收益分布
    print(f"\n📊 超额收益分布:")
    print(f"  • 最高超额收益: {df['excess_return'].max():8.2%}")
    print(f"  • 最低超额收益: {df['excess_return'].min():8.2%}")
    print(f"  • 超额收益 > 20%: {len(df[df['excess_return'] > 0.2])} 个")
    print(f"  • 超额收益 > 0%: {len(df[df['excess_return'] > 0])} 个")
    print(f"  • 超额收益 < -20%: {len(df[df['excess_return'] < -0.2])} 个")
    
    # 最佳表现
    best_strategy = df.loc[df['strategy_return'].idxmax()]
    best_excess = df.loc[df['excess_return'].idxmax()]
    best_sharpe = df.loc[df['sharpe_ratio'].idxmax()]
    
    print(f"\n🏆 最佳表现:")
    print(f"  • 最高策略收益率:")
    print(f"    - 合约: {best_strategy['symbol']}")
    print(f"    - 策略收益率: {best_strategy['strategy_return']:8.2%}")
    print(f"    - 买入持有收益率: {best_strategy['buy_hold_return']:8.2%}")
    print(f"    - 超额收益: {best_strategy['excess_return']:8.2%}")
    print(f"    - 交易次数: {best_strategy['total_trades']:.0f}")
    print(f"    - 胜率: {best_strategy['win_rate']:6.1%}")
    print(f"    - 最大回撤: {best_strategy['max_drawdown']:8.2%}")
    print(f"    - 夏普比率: {best_strategy['sharpe_ratio']:8.2f}")
    
    print(f"\n  • 最高超额收益:")
    print(f"    - 合约: {best_excess['symbol']}")
    print(f"    - 策略收益率: {best_excess['strategy_return']:8.2%}")
    print(f"    - 买入持有收益率: {best_excess['buy_hold_return']:8.2%}")
    print(f"    - 超额收益: {best_excess['excess_return']:8.2%}")
    print(f"    - 交易次数: {best_excess['total_trades']:.0f}")
    print(f"    - 胜率: {best_excess['win_rate']:6.1%}")
    
    print(f"\n  • 最高夏普比率:")
    print(f"    - 合约: {best_sharpe['symbol']}")
    print(f"    - 策略收益率: {best_sharpe['strategy_return']:8.2%}")
    print(f"    - 超额收益: {best_sharpe['excess_return']:8.2%}")
    print(f"    - 夏普比率: {best_sharpe['sharpe_ratio']:8.2f}")
    print(f"    - 最大回撤: {best_sharpe['max_drawdown']:8.2%}")
    
    # 最差表现
    worst_strategy = df.loc[df['strategy_return'].idxmin()]
    worst_excess = df.loc[df['excess_return'].idxmin()]
    worst_sharpe = df.loc[df['sharpe_ratio'].idxmin()]
    
    print(f"\n📉 最差表现:")
    print(f"  • 最低策略收益率:")
    print(f"    - 合约: {worst_strategy['symbol']}")
    print(f"    - 策略收益率: {worst_strategy['strategy_return']:8.2%}")
    print(f"    - 买入持有收益率: {worst_strategy['buy_hold_return']:8.2%}")
    print(f"    - 超额收益: {worst_strategy['excess_return']:8.2%}")
    print(f"    - 交易次数: {worst_strategy['total_trades']:.0f}")
    print(f"    - 胜率: {worst_strategy['win_rate']:6.1%}")
    print(f"    - 最大回撤: {worst_strategy['max_drawdown']:8.2%}")
    
    print(f"\n  • 最低超额收益:")
    print(f"    - 合约: {worst_excess['symbol']}")
    print(f"    - 策略收益率: {worst_excess['strategy_return']:8.2%}")
    print(f"    - 买入持有收益率: {worst_excess['buy_hold_return']:8.2%}")
    print(f"    - 超额收益: {worst_excess['excess_return']:8.2%}")
    print(f"    - 交易次数: {worst_excess['total_trades']:.0f}")
    
    # 按收益率排序的前10名
    print(f"\n🥇 策略收益率前10名:")
    top_10 = df.nlargest(10, 'strategy_return')
    for i, (_, row) in enumerate(top_10.iterrows(), 1):
        print(f"  {i:2d}. {row['symbol']:20s}: {row['strategy_return']:8.2%} (超额: {row['excess_return']:8.2%}, 胜率: {row['win_rate']:6.1%})")
    
    # 按超额收益排序的前10名
    print(f"\n🎯 超额收益前10名:")
    top_excess_10 = df.nlargest(10, 'excess_return')
    for i, (_, row) in enumerate(top_excess_10.iterrows(), 1):
        print(f"  {i:2d}. {row['symbol']:20s}: {row['excess_return']:8.2%} (策略: {row['strategy_return']:8.2%}, 基准: {row['buy_hold_return']:8.2%})")
    
    # 按夏普比率排序的前10名
    print(f"\n📊 夏普比率前10名:")
    top_sharpe_10 = df.nlargest(10, 'sharpe_ratio')
    for i, (_, row) in enumerate(top_sharpe_10.iterrows(), 1):
        print(f"  {i:2d}. {row['symbol']:20s}: {row['sharpe_ratio']:8.2f} (收益: {row['strategy_return']:8.2%}, 回撤: {row['max_drawdown']:8.2%})")
    
    # 策略总结
    print(f"\n📝 策略总结:")
    print(f"  • 该双移动平均线策略在487个USDT永续合约上的平均表现为 {df['strategy_return'].mean():6.2%}")
    print(f"  • 相比买入持有策略，平均超额收益为 {df['excess_return'].mean():6.2%}")
    print(f"  • {positive_returns/len(df)*100:.1f}% 的合约实现了正收益")
    print(f"  • {positive_excess/len(df)*100:.1f}% 的合约跑赢了基准")
    print(f"  • 平均最大回撤为 {df['max_drawdown'].mean():6.2%}，风险控制良好")
    print(f"  • 平均夏普比率为 {df['sharpe_ratio'].mean():6.2f}，表明风险调整后收益一般")
    
    if df['excess_return'].mean() > 0:
        print(f"  • ✅ 策略整体跑赢基准，具有一定的alpha价值")
    else:
        print(f"  • ❌ 策略整体未跑赢基准，需要进一步优化")
    
    print(f"\n✅ 分析完成！详细结果已保存在 data/backtest_results/ 目录下")

if __name__ == "__main__":
    analyze_backtest_results()
