# -*- coding: utf-8 -*-
"""
展示已有策略的资金曲线
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import json
import os
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_strategy_results():
    """加载所有策略结果"""
    strategies = {}
    
    # BTC-ETH选优策略
    btc_eth_dir = "data/btc_eth_strategy_results"
    if os.path.exists(btc_eth_dir):
        try:
            # 加载详细结果
            results_file = os.path.join(btc_eth_dir, "detailed_results.csv")
            performance_file = os.path.join(btc_eth_dir, "performance_metrics.json")
            
            if os.path.exists(results_file) and os.path.exists(performance_file):
                results_df = pd.read_csv(results_file)
                with open(performance_file, 'r', encoding='utf-8') as f:
                    performance = json.load(f)
                
                results_df['date'] = pd.to_datetime(results_df['date'])
                
                strategies['BTC-ETH选优策略'] = {
                    'data': results_df,
                    'performance': performance,
                    'color': 'blue'
                }
                print(f"✅ 加载BTC-ETH选优策略: {len(results_df)} 条记录")
        except Exception as e:
            print(f"⚠️ 加载BTC-ETH策略失败: {e}")
    
    return strategies

def plot_equity_curves(strategies):
    """绘制资金曲线对比图"""
    if not strategies:
        print("❌ 没有找到策略结果数据")
        return
    
    print("📊 生成资金曲线对比图...")
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('加密货币量化交易策略回测结果对比', fontsize=16, fontweight='bold')
    
    # 1. 资金曲线对比
    ax1 = axes[0, 0]
    
    for strategy_name, strategy_data in strategies.items():
        data = strategy_data['data']
        color = strategy_data['color']
        performance = strategy_data['performance']
        
        # 绘制策略曲线
        ax1.plot(data['date'], data['capital'], 
                label=f"{strategy_name} ({performance['strategy_total_return']:.1%})", 
                linewidth=2, color=color)
        
        # 绘制基准曲线
        ax1.plot(data['date'], data['benchmark_capital'], 
                label=f"等权重基准 ({performance['benchmark_total_return']:.1%})", 
                linewidth=2, color='red', alpha=0.7, linestyle='--')
    
    ax1.set_title('资金曲线对比', fontsize=14, fontweight='bold')
    ax1.set_xlabel('日期')
    ax1.set_ylabel('资金 (USDT)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 格式化日期轴
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    
    # 2. 回撤对比
    ax2 = axes[0, 1]
    
    for strategy_name, strategy_data in strategies.items():
        data = strategy_data['data']
        color = strategy_data['color']
        performance = strategy_data['performance']
        
        # 计算回撤
        data['strategy_peak'] = data['capital'].cummax()
        data['strategy_drawdown'] = (data['capital'] - data['strategy_peak']) / data['strategy_peak']
        
        ax2.fill_between(data['date'], data['strategy_drawdown'], 0, 
                        alpha=0.3, color=color, 
                        label=f"{strategy_name} (最大: {performance['max_drawdown']:.1%})")
    
    ax2.set_title('回撤对比', fontsize=14, fontweight='bold')
    ax2.set_xlabel('日期')
    ax2.set_ylabel('回撤比例')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax2.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
    
    # 3. 性能指标对比
    ax3 = axes[1, 0]
    ax3.axis('off')
    
    # 创建性能对比表
    metrics_data = [['策略', '总收益率', '年化收益率', '最大回撤', '夏普比率', '胜率']]
    
    for strategy_name, strategy_data in strategies.items():
        perf = strategy_data['performance']
        metrics_data.append([
            strategy_name,
            f"{perf['strategy_total_return']:.2%}",
            f"{perf['strategy_annual_return']:.2%}",
            f"{perf['max_drawdown']:.2%}",
            f"{perf['strategy_sharpe']:.3f}",
            f"{perf['win_rate']:.2%}"
        ])
    
    # 添加基准数据
    if strategies:
        first_strategy = list(strategies.values())[0]
        perf = first_strategy['performance']
        metrics_data.append([
            '等权重基准',
            f"{perf['benchmark_total_return']:.2%}",
            f"{perf['benchmark_annual_return']:.2%}",
            f"{perf['benchmark_max_drawdown']:.2%}",
            f"{perf['benchmark_sharpe']:.3f}",
            '-'
        ])
    
    table = ax3.table(cellText=metrics_data[1:], colLabels=metrics_data[0], 
                     cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(metrics_data)):
        for j in range(len(metrics_data[0])):
            if i == 0:  # 表头
                table[(i, j)].set_facecolor('#4CAF50')
                table[(i, j)].set_text_props(weight='bold', color='white')
            elif j == 0:  # 第一列
                table[(i, j)].set_facecolor('#E8F5E8')
                table[(i, j)].set_text_props(weight='bold')
            else:
                table[(i, j)].set_facecolor('#F5F5F5')
    
    ax3.set_title('策略性能指标对比', fontsize=14, fontweight='bold', pad=20)
    
    # 4. 月度收益分布
    ax4 = axes[1, 1]
    
    if strategies:
        strategy_name, strategy_data = list(strategies.items())[0]
        data = strategy_data['data']
        
        # 计算月度收益率
        data['year_month'] = data['date'].dt.to_period('M')
        monthly_returns = data.groupby('year_month').agg({
            'capital': ['first', 'last']
        })
        monthly_returns.columns = ['start', 'end']
        monthly_returns['monthly_return'] = (monthly_returns['end'] / monthly_returns['start'] - 1) * 100
        
        # 绘制月度收益分布
        ax4.hist(monthly_returns['monthly_return'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax4.axvline(monthly_returns['monthly_return'].mean(), color='red', linestyle='--', 
                   label=f'平均: {monthly_returns["monthly_return"].mean():.1f}%')
        ax4.set_title('月度收益率分布', fontsize=14, fontweight='bold')
        ax4.set_xlabel('月度收益率 (%)')
        ax4.set_ylabel('频次')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    chart_file = "data/strategy_comparison.png"
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    print(f"📊 图表已保存: {chart_file}")
    
    plt.show()
    
    return chart_file

def show_strategy_summary(strategies):
    """显示策略总结"""
    print("\n🎉 策略回测结果总结")
    print("=" * 80)
    
    for strategy_name, strategy_data in strategies.items():
        perf = strategy_data['performance']
        data = strategy_data['data']
        
        print(f"\n📈 {strategy_name}:")
        print(f"  • 回测期间: {perf['years']:.1f} 年")
        print(f"  • 交易天数: {len(data):,} 天")
        print(f"  • 策略总收益: {perf['strategy_total_return']:.2%}")
        print(f"  • 基准总收益: {perf['benchmark_total_return']:.2%}")
        print(f"  • 超额收益: {perf['excess_return']:.2%}")
        print(f"  • 年化收益率: {perf['strategy_annual_return']:.2%}")
        print(f"  • 最大回撤: {perf['max_drawdown']:.2%}")
        print(f"  • 夏普比率: {perf['strategy_sharpe']:.3f}")
        print(f"  • 胜率: {perf['win_rate']:.2%}")
        print(f"  • 最终资金: {perf['final_capital']:,.0f} USDT")
        
        # 策略评价
        if perf['strategy_sharpe'] > perf['benchmark_sharpe'] and abs(perf['max_drawdown']) < abs(perf['benchmark_max_drawdown']):
            print(f"  • 🌟 综合评价: 优秀 - 风险调整收益和回撤控制均优于基准")
        elif perf['excess_return'] > 0:
            print(f"  • ✅ 综合评价: 良好 - 跑赢基准，有超额收益")
        else:
            print(f"  • ⚠️ 综合评价: 一般 - 需要进一步优化")

def main():
    """主函数"""
    print("🚀 展示策略资金曲线...")
    
    # 加载策略结果
    strategies = load_strategy_results()
    
    if not strategies:
        print("❌ 没有找到任何策略结果")
        print("💡 请先运行策略回测程序生成结果")
        return
    
    # 绘制资金曲线
    chart_file = plot_equity_curves(strategies)
    
    # 显示策略总结
    show_strategy_summary(strategies)
    
    print(f"\n📁 图表文件: {chart_file}")
    print("✅ 资金曲线展示完成！")

if __name__ == "__main__":
    main()
