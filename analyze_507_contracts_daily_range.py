#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
507合约每日实体涨跌幅度分析工具

功能：
1. 分析507个USDT永续合约的每日实体涨跌幅度
2. 计算实体大小（收盘价与开盘价的差值）
3. 统计涨跌幅度分布
4. 生成可视化图表和分析报告

作者：加密货币量化交易系统
日期：2025年1月28日
"""

import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import sys
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class Contract507DailyRangeAnalyzer:
    """507合约每日实体涨跌幅度分析器"""
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化分析器
        
        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = data_dir
        self.contracts_data = None
        self.analysis_results = {}
        
        print("🚀 507合约每日实体涨跌幅度分析器初始化完成")
        print("=" * 60)
    
    def load_contracts_data(self) -> bool:
        """
        加载507个合约的最新数据
        
        Returns:
            bool: 是否成功加载数据
        """
        try:
            # 加载最新的507合约数据
            contracts_file = os.path.join(self.data_dir, "all_perpetual_contracts_last_dates.json")
            
            if not os.path.exists(contracts_file):
                print(f"❌ 未找到合约数据文件: {contracts_file}")
                return False
            
            with open(contracts_file, 'r', encoding='utf-8') as f:
                self.contracts_data = json.load(f)
            
            print(f"✅ 成功加载507合约数据")
            print(f"📊 数据获取时间: {self.contracts_data['fetch_time']}")
            print(f"📈 合约总数: {self.contracts_data['total_symbols']}")
            print(f"✅ 成功获取: {self.contracts_data['successful_count']}")
            print(f"❌ 已下线: {self.contracts_data['delisted_count']}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载合约数据失败: {str(e)}")
            return False
    
    def calculate_daily_ranges(self) -> pd.DataFrame:
        """
        计算每个合约的每日实体涨跌幅度
        
        Returns:
            pd.DataFrame: 包含实体涨跌幅度的数据框
        """
        print("\n📊 开始计算每日实体涨跌幅度...")
        
        results = []
        
        for contract in self.contracts_data['results']:
            symbol = contract['symbol']
            
            # 跳过失败的合约
            if contract['status'] != 'SUCCESS':
                continue
            
            # 获取OHLC数据
            open_price = contract['open']
            high_price = contract['high']
            low_price = contract['low']
            close_price = contract['close']
            volume = contract['volume']
            
            # 计算实体大小（收盘价 - 开盘价）
            body_size = close_price - open_price
            body_size_pct = (body_size / open_price) * 100 if open_price > 0 else 0
            
            # 计算上下影线
            upper_shadow = high_price - max(open_price, close_price)
            lower_shadow = min(open_price, close_price) - low_price
            
            # 计算上下影线百分比
            upper_shadow_pct = (upper_shadow / open_price) * 100 if open_price > 0 else 0
            lower_shadow_pct = (lower_shadow / open_price) * 100 if open_price > 0 else 0
            
            # 计算总振幅
            total_range = high_price - low_price
            total_range_pct = (total_range / open_price) * 100 if open_price > 0 else 0
            
            # 判断K线类型
            if body_size > 0:
                candle_type = "阳线"
            elif body_size < 0:
                candle_type = "阴线"
            else:
                candle_type = "十字星"
            
            # 计算实体占总振幅的比例
            body_ratio = abs(body_size) / total_range if total_range > 0 else 0
            
            results.append({
                'symbol': symbol,
                'date': contract['last_date'],
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume,
                'body_size': body_size,
                'body_size_pct': body_size_pct,
                'upper_shadow': upper_shadow,
                'lower_shadow': lower_shadow,
                'upper_shadow_pct': upper_shadow_pct,
                'lower_shadow_pct': lower_shadow_pct,
                'total_range': total_range,
                'total_range_pct': total_range_pct,
                'candle_type': candle_type,
                'body_ratio': body_ratio,
                'contract_status': contract.get('contract_status', 'UNKNOWN')
            })
        
        df = pd.DataFrame(results)
        print(f"✅ 成功计算 {len(df)} 个合约的实体涨跌幅度")
        
        return df
    
    def analyze_statistics(self, df: pd.DataFrame) -> Dict:
        """
        分析统计数据
        
        Args:
            df: 包含实体涨跌幅度的数据框
            
        Returns:
            Dict: 统计分析结果
        """
        print("\n📈 开始统计分析...")
        
        stats = {}
        
        # 基础统计
        stats['total_contracts'] = len(df)
        stats['active_contracts'] = len(df[df['contract_status'] == 'ACTIVE'])
        
        # 实体大小统计
        stats['body_size_stats'] = {
            'mean': df['body_size_pct'].mean(),
            'median': df['body_size_pct'].median(),
            'std': df['body_size_pct'].std(),
            'min': df['body_size_pct'].min(),
            'max': df['body_size_pct'].max(),
            'q25': df['body_size_pct'].quantile(0.25),
            'q75': df['body_size_pct'].quantile(0.75)
        }
        
        # K线类型分布
        candle_counts = df['candle_type'].value_counts()
        stats['candle_distribution'] = {
            '阳线数量': candle_counts.get('阳线', 0),
            '阴线数量': candle_counts.get('阴线', 0),
            '十字星数量': candle_counts.get('十字星', 0),
            '阳线比例': candle_counts.get('阳线', 0) / len(df) * 100,
            '阴线比例': candle_counts.get('阴线', 0) / len(df) * 100
        }
        
        # 振幅统计
        stats['range_stats'] = {
            'mean': df['total_range_pct'].mean(),
            'median': df['total_range_pct'].median(),
            'std': df['total_range_pct'].std(),
            'min': df['total_range_pct'].min(),
            'max': df['total_range_pct'].max()
        }
        
        # 实体比例统计
        stats['body_ratio_stats'] = {
            'mean': df['body_ratio'].mean(),
            'median': df['body_ratio'].median(),
            'high_body_ratio': len(df[df['body_ratio'] > 0.7]) / len(df) * 100,  # 实体占比>70%
            'low_body_ratio': len(df[df['body_ratio'] < 0.3]) / len(df) * 100    # 实体占比<30%
        }
        
        print(f"✅ 统计分析完成")
        
        return stats
    
    def create_visualizations(self, df: pd.DataFrame, stats: Dict) -> str:
        """
        创建可视化图表
        
        Args:
            df: 数据框
            stats: 统计结果
            
        Returns:
            str: 图表保存路径
        """
        print("\n📊 开始创建可视化图表...")
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('507合约每日实体涨跌幅度分析', fontsize=16, fontweight='bold')
        
        # 1. 实体大小分布直方图
        axes[0, 0].hist(df['body_size_pct'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('实体大小分布（%）')
        axes[0, 0].set_xlabel('实体大小百分比')
        axes[0, 0].set_ylabel('合约数量')
        axes[0, 0].axvline(0, color='red', linestyle='--', alpha=0.7, label='零线')
        axes[0, 0].legend()
        
        # 2. K线类型饼图
        candle_counts = df['candle_type'].value_counts()
        colors = ['lightgreen', 'lightcoral', 'gold']
        axes[0, 1].pie(candle_counts.values, labels=candle_counts.index, autopct='%1.1f%%', 
                       colors=colors, startangle=90)
        axes[0, 1].set_title('K线类型分布')
        
        # 3. 总振幅分布
        axes[0, 2].hist(df['total_range_pct'], bins=50, alpha=0.7, color='orange', edgecolor='black')
        axes[0, 2].set_title('总振幅分布（%）')
        axes[0, 2].set_xlabel('总振幅百分比')
        axes[0, 2].set_ylabel('合约数量')
        
        # 4. 实体大小 vs 总振幅散点图
        colors_map = {'阳线': 'green', '阴线': 'red', '十字星': 'gray'}
        for candle_type in df['candle_type'].unique():
            subset = df[df['candle_type'] == candle_type]
            axes[1, 0].scatter(subset['total_range_pct'], subset['body_size_pct'], 
                              alpha=0.6, label=candle_type, color=colors_map.get(candle_type, 'blue'))
        axes[1, 0].set_xlabel('总振幅（%）')
        axes[1, 0].set_ylabel('实体大小（%）')
        axes[1, 0].set_title('实体大小 vs 总振幅')
        axes[1, 0].legend()
        axes[1, 0].axhline(0, color='black', linestyle='-', alpha=0.3)
        
        # 5. 实体比例分布
        axes[1, 1].hist(df['body_ratio'], bins=30, alpha=0.7, color='purple', edgecolor='black')
        axes[1, 1].set_title('实体占总振幅比例分布')
        axes[1, 1].set_xlabel('实体比例')
        axes[1, 1].set_ylabel('合约数量')
        
        # 6. 成交量 vs 实体大小
        axes[1, 2].scatter(df['volume'], abs(df['body_size_pct']), alpha=0.6, color='brown')
        axes[1, 2].set_xlabel('成交量')
        axes[1, 2].set_ylabel('实体大小绝对值（%）')
        axes[1, 2].set_title('成交量 vs 实体大小')
        axes[1, 2].set_xscale('log')  # 对数坐标
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = f"507_contracts_daily_range_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 图表已保存: {chart_file}")
        
        return chart_file
    
    def generate_report(self, df: pd.DataFrame, stats: Dict) -> str:
        """
        生成分析报告
        
        Args:
            df: 数据框
            stats: 统计结果
            
        Returns:
            str: 报告文件路径
        """
        print("\n📝 开始生成分析报告...")
        
        report_content = f"""
# 507合约每日实体涨跌幅度分析报告

## 📊 基础信息
- **分析日期**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **数据来源**: 币安官方期货API
- **合约总数**: {stats['total_contracts']}
- **活跃合约**: {stats['active_contracts']}
- **数据日期**: {df['date'].iloc[0] if len(df) > 0 else 'N/A'}

## 📈 实体大小统计
- **平均实体大小**: {stats['body_size_stats']['mean']:.3f}%
- **中位数实体大小**: {stats['body_size_stats']['median']:.3f}%
- **标准差**: {stats['body_size_stats']['std']:.3f}%
- **最大涨幅**: {stats['body_size_stats']['max']:.3f}%
- **最大跌幅**: {stats['body_size_stats']['min']:.3f}%
- **25%分位数**: {stats['body_size_stats']['q25']:.3f}%
- **75%分位数**: {stats['body_size_stats']['q75']:.3f}%

## 🕯️ K线类型分布
- **阳线数量**: {stats['candle_distribution']['阳线数量']} ({stats['candle_distribution']['阳线比例']:.1f}%)
- **阴线数量**: {stats['candle_distribution']['阴线数量']} ({stats['candle_distribution']['阴线比例']:.1f}%)
- **十字星数量**: {stats['candle_distribution']['十字星数量']}

## 📏 振幅统计
- **平均总振幅**: {stats['range_stats']['mean']:.3f}%
- **中位数总振幅**: {stats['range_stats']['median']:.3f}%
- **最大振幅**: {stats['range_stats']['max']:.3f}%
- **最小振幅**: {stats['range_stats']['min']:.3f}%

## 🎯 实体比例分析
- **平均实体比例**: {stats['body_ratio_stats']['mean']:.3f}
- **中位数实体比例**: {stats['body_ratio_stats']['median']:.3f}
- **高实体比例合约**: {stats['body_ratio_stats']['high_body_ratio']:.1f}% (实体占比>70%)
- **低实体比例合约**: {stats['body_ratio_stats']['low_body_ratio']:.1f}% (实体占比<30%)

## 🔝 极值合约

### 最大涨幅合约 (Top 10)
"""
        
        # 添加最大涨幅合约
        top_gainers = df.nlargest(10, 'body_size_pct')[['symbol', 'body_size_pct', 'total_range_pct', 'volume']]
        for idx, row in top_gainers.iterrows():
            report_content += f"- **{row['symbol']}**: +{row['body_size_pct']:.3f}% (振幅: {row['total_range_pct']:.3f}%)\n"
        
        report_content += "\n### 最大跌幅合约 (Top 10)\n"
        
        # 添加最大跌幅合约
        top_losers = df.nsmallest(10, 'body_size_pct')[['symbol', 'body_size_pct', 'total_range_pct', 'volume']]
        for idx, row in top_losers.iterrows():
            report_content += f"- **{row['symbol']}**: {row['body_size_pct']:.3f}% (振幅: {row['total_range_pct']:.3f}%)\n"
        
        report_content += "\n### 最大振幅合约 (Top 10)\n"
        
        # 添加最大振幅合约
        top_ranges = df.nlargest(10, 'total_range_pct')[['symbol', 'total_range_pct', 'body_size_pct', 'volume']]
        for idx, row in top_ranges.iterrows():
            report_content += f"- **{row['symbol']}**: {row['total_range_pct']:.3f}% (实体: {row['body_size_pct']:.3f}%)\n"
        
        report_content += f"""

## 📋 数据质量
- **数据来源**: {self.contracts_data['data_source']}
- **API端点**: {self.contracts_data['api_endpoint']}
- **数据真实性**: {self.contracts_data['data_authenticity']}
- **获取时间**: {self.contracts_data['fetch_time']}

## 🎯 分析结论

1. **市场整体表现**: {'偏多' if stats['candle_distribution']['阳线比例'] > 50 else '偏空'}，阳线比例为{stats['candle_distribution']['阳线比例']:.1f}%

2. **波动性分析**: 平均振幅为{stats['range_stats']['mean']:.3f}%，显示市场{'高波动' if stats['range_stats']['mean'] > 5 else '中等波动' if stats['range_stats']['mean'] > 2 else '低波动'}

3. **实体特征**: 平均实体大小为{stats['body_size_stats']['mean']:.3f}%，{'实体较大' if abs(stats['body_size_stats']['mean']) > 2 else '实体适中'}

4. **交易建议**: 基于当前数据，建议关注高振幅、高实体比例的合约进行短线交易

---

**报告生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}  
**数据来源**: 币安官方期货API  
**分析工具**: 507合约每日实体涨跌幅度分析器
"""
        
        # 保存报告
        report_file = f"507_contracts_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"✅ 分析报告已保存: {report_file}")
        
        return report_file
    
    def run_analysis(self) -> bool:
        """
        运行完整分析流程
        
        Returns:
            bool: 是否成功完成分析
        """
        try:
            # 1. 加载数据
            if not self.load_contracts_data():
                return False
            
            # 2. 计算实体涨跌幅度
            df = self.calculate_daily_ranges()
            if df.empty:
                print("❌ 没有有效的合约数据")
                return False
            
            # 3. 统计分析
            stats = self.analyze_statistics(df)
            
            # 4. 创建可视化
            chart_file = self.create_visualizations(df, stats)
            
            # 5. 生成报告
            report_file = self.generate_report(df, stats)
            
            # 6. 保存详细数据
            detail_file = f"507_contracts_detail_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(detail_file, index=False, encoding='utf-8-sig')
            
            print(f"\n🎉 507合约每日实体涨跌幅度分析完成！")
            print(f"📊 图表文件: {chart_file}")
            print(f"📝 报告文件: {report_file}")
            print(f"📋 详细数据: {detail_file}")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析过程中发生错误: {str(e)}")
            return False

def main():
    """主函数"""
    print("🚀 启动507合约每日实体涨跌幅度分析...")
    
    # 创建分析器
    analyzer = Contract507DailyRangeAnalyzer()
    
    # 运行分析
    success = analyzer.run_analysis()
    
    if success:
        print("\n✅ 分析完成！")
    else:
        print("\n❌ 分析失败！")
    
    return success

if __name__ == "__main__":
    main()
