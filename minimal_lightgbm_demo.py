# -*- coding: utf-8 -*-
"""
最简化的LightGBM预测演示：快速展示资金曲线
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
import os

# 设置中文字体和忽略警告
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

# 尝试导入LightGBM
try:
    import lightgbm as lgb
    USE_LIGHTGBM = True
    print("✅ 使用LightGBM模型")
except ImportError:
    USE_LIGHTGBM = False
    print("⚠️ LightGBM未安装，使用简单线性回归")

def load_btc_eth_data():
    """加载BTC和ETH数据"""
    print("📊 加载BTC和ETH数据...")
    
    data_file = "data/btc_eth_data/btc_eth_combined.csv"
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return None
    
    df = pd.read_csv(data_file)
    df['date'] = pd.to_datetime(df['date'])
    
    print(f"✅ 数据加载完成: {len(df)} 条记录")
    return df

def create_simple_features(df):
    """创建最简单的特征"""
    print("🔧 创建简单特征...")
    
    result_data = []
    
    for symbol in ['BTCUSDT', 'ETHUSDT']:
        symbol_df = df[df['symbol'] == symbol].copy().sort_values('date').reset_index(drop=True)
        
        # 基础特征
        symbol_df['returns'] = symbol_df['close'].pct_change()
        
        # 简单滞后特征
        symbol_df['returns_lag_1'] = symbol_df['returns'].shift(1)
        symbol_df['returns_lag_2'] = symbol_df['returns'].shift(2)
        symbol_df['returns_lag_3'] = symbol_df['returns'].shift(3)
        
        # 简单移动平均
        symbol_df['ma5'] = symbol_df['close'].rolling(5).mean()
        symbol_df['ma_ratio'] = symbol_df['close'] / symbol_df['ma5']
        
        # 目标变量
        symbol_df['target'] = symbol_df['returns'].shift(-1)
        
        result_data.append(symbol_df)
    
    combined_df = pd.concat(result_data, ignore_index=True)
    print("✅ 特征创建完成")
    return combined_df

def simple_predict(X, y):
    """简单预测函数"""
    if USE_LIGHTGBM:
        # 使用LightGBM
        train_data = lgb.Dataset(X, label=y)
        params = {
            'objective': 'regression',
            'metric': 'rmse',
            'verbose': -1,
            'random_state': 42
        }
        model = lgb.train(params, train_data, num_boost_round=20, callbacks=[lgb.log_evaluation(0)])
        return model
    else:
        # 使用简单线性回归
        X_mean = np.mean(X, axis=0)
        y_mean = np.mean(y)
        return {'X_mean': X_mean, 'y_mean': y_mean}

def predict_with_model(model, X):
    """使用模型预测"""
    if USE_LIGHTGBM:
        return model.predict(X)
    else:
        # 简单预测：基于历史平均
        return np.full(X.shape[0], model['y_mean'])

def run_simple_backtest():
    """运行简单回测"""
    print("🚀 开始简化版LightGBM预测策略演示...")
    print("=" * 60)
    
    # 1. 加载数据
    df = load_btc_eth_data()
    if df is None:
        return False
    
    # 2. 创建特征
    features_df = create_simple_features(df)
    
    # 3. 准备数据
    feature_cols = ['returns_lag_1', 'returns_lag_2', 'returns_lag_3', 'ma_ratio']
    clean_df = features_df[['date', 'symbol', 'close', 'target'] + feature_cols].dropna()
    
    print(f"📊 清理后数据: {len(clean_df)} 条")
    
    # 4. 简单预测
    predictions = {}
    train_window = 50  # 小训练窗口
    
    for symbol in ['BTCUSDT', 'ETHUSDT']:
        print(f"📈 预测 {symbol}...")
        
        symbol_df = clean_df[clean_df['symbol'] == symbol].copy().sort_values('date').reset_index(drop=True)
        
        pred_results = []
        
        # 只做少量预测以加快速度
        for i in range(train_window, min(train_window + 100, len(symbol_df) - 1)):
            # 训练数据
            train_data = symbol_df.iloc[i-train_window:i]
            X_train = train_data[feature_cols].values
            y_train = train_data['target'].values
            
            # 移除NaN
            valid_idx = ~(np.isnan(X_train).any(axis=1) | np.isnan(y_train))
            X_train = X_train[valid_idx]
            y_train = y_train[valid_idx]
            
            if len(X_train) < 10:
                continue
            
            try:
                # 训练模型
                model = simple_predict(X_train, y_train)
                
                # 预测
                X_test = symbol_df.iloc[i][feature_cols].values.reshape(1, -1)
                if not np.isnan(X_test).any():
                    pred = predict_with_model(model, X_test)[0]
                    actual = symbol_df.iloc[i]['target']
                    
                    pred_results.append({
                        'date': symbol_df.iloc[i]['date'],
                        'prediction': pred,
                        'actual': actual
                    })
            except Exception as e:
                continue
        
        predictions[symbol] = pd.DataFrame(pred_results)
        print(f"  • {symbol} 预测完成: {len(pred_results)} 个预测")
    
    # 5. 简单回测
    print("📊 执行策略回测...")
    
    btc_dates = set(predictions['BTCUSDT']['date'])
    eth_dates = set(predictions['ETHUSDT']['date'])
    common_dates = sorted(btc_dates & eth_dates)
    
    results = []
    capital = 100000  # 初始资金
    benchmark_capital = 100000
    position = None
    
    for date in common_dates:
        # 获取预测数据
        btc_data = predictions['BTCUSDT'][predictions['BTCUSDT']['date'] == date]
        eth_data = predictions['ETHUSDT'][predictions['ETHUSDT']['date'] == date]
        
        if len(btc_data) == 0 or len(eth_data) == 0:
            continue
        
        btc_pred = btc_data.iloc[0]['prediction']
        eth_pred = eth_data.iloc[0]['prediction']
        btc_actual = btc_data.iloc[0]['actual']
        eth_actual = eth_data.iloc[0]['actual']
        
        # 策略逻辑
        if btc_pred > eth_pred and btc_pred > 0.001:
            new_position = 'BTCUSDT'
            selected_return = btc_actual
        elif eth_pred > btc_pred and eth_pred > 0.001:
            new_position = 'ETHUSDT'
            selected_return = eth_actual
        else:
            new_position = None
            selected_return = 0
        
        # 计算收益
        if new_position is not None:
            net_return = selected_return - 0.001  # 交易费用
            capital *= (1 + net_return)
        
        position = new_position
        
        # 基准收益
        benchmark_return = (btc_actual + eth_actual) / 2
        if not pd.isna(benchmark_return):
            benchmark_capital *= (1 + benchmark_return - 0.0005)
        
        results.append({
            'date': date,
            'capital': capital,
            'benchmark_capital': benchmark_capital,
            'position': position,
            'btc_pred': btc_pred,
            'eth_pred': eth_pred
        })
    
    results_df = pd.DataFrame(results)
    
    # 6. 绘制资金曲线
    print("📊 生成资金曲线图...")
    
    # 计算指标
    strategy_return = (results_df['capital'].iloc[-1] / 100000) - 1
    benchmark_return = (results_df['benchmark_capital'].iloc[-1] / 100000) - 1
    
    # 预测准确性
    btc_corr = np.corrcoef(results_df['btc_pred'].dropna(), 
                          [predictions['BTCUSDT'][predictions['BTCUSDT']['date'] == d]['actual'].iloc[0] 
                           for d in results_df['date'] if len(predictions['BTCUSDT'][predictions['BTCUSDT']['date'] == d]) > 0])[0,1] if len(results_df) > 1 else 0
    
    # 创建图表
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    model_name = "LightGBM" if USE_LIGHTGBM else "线性回归"
    fig.suptitle(f'{model_name}预测策略演示结果', fontsize=16, fontweight='bold')
    
    # 资金曲线
    ax1 = axes[0]
    ax1.plot(results_df['date'], results_df['capital'], label=f'{model_name}策略', linewidth=2, color='blue')
    ax1.plot(results_df['date'], results_df['benchmark_capital'], label='等权重基准', linewidth=2, color='red', alpha=0.7)
    ax1.set_title('资金曲线对比', fontsize=14, fontweight='bold')
    ax1.set_xlabel('日期')
    ax1.set_ylabel('资金 (USDT)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加收益率标注
    ax1.text(0.02, 0.98, f'策略收益: {strategy_return:.2%}', transform=ax1.transAxes, 
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    ax1.text(0.02, 0.90, f'基准收益: {benchmark_return:.2%}', transform=ax1.transAxes, 
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
    
    # 持仓分布
    ax2 = axes[1]
    position_counts = results_df['position'].value_counts()
    if len(position_counts) > 0:
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
        wedges, texts, autotexts = ax2.pie(position_counts.values, labels=position_counts.index, 
                                          autopct='%1.1f%%', colors=colors[:len(position_counts)])
        ax2.set_title('持仓分布', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    
    # 保存图表
    os.makedirs("data/minimal_lightgbm_results", exist_ok=True)
    chart_file = "data/minimal_lightgbm_results/demo_results.png"
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    print(f"📊 图表已保存: {chart_file}")
    plt.show()
    
    # 显示结果
    print(f"\n🎉 {model_name}预测策略演示完成！")
    print("=" * 60)
    print(f"📈 策略表现:")
    print(f"  • 演示期间: {len(results_df)} 天")
    print(f"  • 策略总收益: {strategy_return:.2%}")
    print(f"  • 基准总收益: {benchmark_return:.2%}")
    print(f"  • 超额收益: {strategy_return - benchmark_return:.2%}")
    print(f"  • 最终资金: {results_df['capital'].iloc[-1]:,.0f} USDT")
    print(f"  • 预测相关性: {btc_corr:.3f}")
    print(f"📁 结果保存在: data/minimal_lightgbm_results/")
    
    return True

def main():
    """主函数"""
    success = run_simple_backtest()
    
    if success:
        print("\n✅ 演示程序执行成功")
    else:
        print("\n❌ 演示程序执行失败")

if __name__ == "__main__":
    main()
