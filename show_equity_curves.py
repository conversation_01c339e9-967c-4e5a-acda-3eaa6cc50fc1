# -*- coding: utf-8 -*-
"""
展示已有策略的资金曲线图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import json
import os
from PIL import Image

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def show_existing_charts():
    """展示已有的策略图表"""
    
    print("📊 展示已有策略的资金曲线图")
    print("=" * 60)
    
    # 检查已有的策略结果
    strategy_dirs = [
        "data/btc_eth_strategy_results",
        "data/simple_ml_results", 
        "data/ultra_simple_results",
        "data/minimal_lightgbm_results",
        "data/ml_prediction_results"
    ]
    
    found_strategies = []
    
    for strategy_dir in strategy_dirs:
        if os.path.exists(strategy_dir):
            # 检查是否有图表文件
            chart_files = []
            for file in os.listdir(strategy_dir):
                if file.endswith('.png'):
                    chart_files.append(os.path.join(strategy_dir, file))
            
            # 检查是否有性能指标文件
            performance_file = os.path.join(strategy_dir, "performance_metrics.json")
            if os.path.exists(performance_file):
                with open(performance_file, 'r', encoding='utf-8') as f:
                    performance = json.load(f)
                
                found_strategies.append({
                    'name': strategy_dir.split('/')[-1],
                    'dir': strategy_dir,
                    'charts': chart_files,
                    'performance': performance
                })
    
    if not found_strategies:
        print("❌ 未找到已完成的策略回测结果")
        return
    
    print(f"✅ 找到 {len(found_strategies)} 个已完成的策略:")
    
    for i, strategy in enumerate(found_strategies, 1):
        print(f"\n{i}. {strategy['name']}")
        print(f"   📁 目录: {strategy['dir']}")
        print(f"   📊 图表: {len(strategy['charts'])} 个")
        
        # 显示性能指标
        perf = strategy['performance']
        if 'strategy_total_return' in perf:
            print(f"   💰 策略总收益: {perf['strategy_total_return']:.2%}")
        if 'benchmark_total_return' in perf:
            print(f"   📈 基准总收益: {perf['benchmark_total_return']:.2%}")
        if 'max_drawdown' in perf:
            print(f"   📉 最大回撤: {perf['max_drawdown']:.2%}")
        if 'sharpe_ratio' in perf:
            print(f"   📊 夏普比率: {perf['sharpe_ratio']:.3f}")
        if 'final_capital' in perf:
            print(f"   💵 最终资金: {perf['final_capital']:,.0f} USDT")
    
    return found_strategies

def create_summary_chart(strategies):
    """创建策略对比图表"""
    
    if not strategies:
        return
    
    print(f"\n📊 创建策略对比图表...")
    
    # 提取策略数据
    strategy_names = []
    total_returns = []
    max_drawdowns = []
    sharpe_ratios = []
    final_capitals = []
    
    for strategy in strategies:
        perf = strategy['performance']
        strategy_names.append(strategy['name'].replace('_', ' ').title())
        
        total_returns.append(perf.get('strategy_total_return', 0) * 100)
        max_drawdowns.append(abs(perf.get('max_drawdown', 0)) * 100)
        sharpe_ratios.append(perf.get('sharpe_ratio', 0))
        final_capitals.append(perf.get('final_capital', 100000))
    
    # 创建对比图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('策略回测结果对比', fontsize=16, fontweight='bold')
    
    # 1. 总收益率对比
    ax1 = axes[0, 0]
    bars1 = ax1.bar(strategy_names, total_returns, color=['blue', 'green', 'red', 'orange', 'purple'][:len(strategy_names)])
    ax1.set_title('总收益率对比 (%)')
    ax1.set_ylabel('收益率 (%)')
    ax1.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, value in zip(bars1, total_returns):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(total_returns)*0.01, 
                f'{value:.1f}%', ha='center', va='bottom')
    
    # 2. 最大回撤对比
    ax2 = axes[0, 1]
    bars2 = ax2.bar(strategy_names, max_drawdowns, color=['red', 'orange', 'yellow', 'green', 'blue'][:len(strategy_names)])
    ax2.set_title('最大回撤对比 (%)')
    ax2.set_ylabel('回撤 (%)')
    ax2.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, value in zip(bars2, max_drawdowns):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(max_drawdowns)*0.01, 
                f'{value:.1f}%', ha='center', va='bottom')
    
    # 3. 夏普比率对比
    ax3 = axes[1, 0]
    bars3 = ax3.bar(strategy_names, sharpe_ratios, color=['purple', 'blue', 'green', 'orange', 'red'][:len(strategy_names)])
    ax3.set_title('夏普比率对比')
    ax3.set_ylabel('夏普比率')
    ax3.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, value in zip(bars3, sharpe_ratios):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(sharpe_ratios)*0.01, 
                f'{value:.3f}', ha='center', va='bottom')
    
    # 4. 最终资金对比
    ax4 = axes[1, 1]
    final_capitals_k = [x/1000 for x in final_capitals]  # 转换为千USDT
    bars4 = ax4.bar(strategy_names, final_capitals_k, color=['gold', 'silver', 'bronze', 'lightblue', 'lightgreen'][:len(strategy_names)])
    ax4.set_title('最终资金对比 (千USDT)')
    ax4.set_ylabel('资金 (千USDT)')
    ax4.tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, value in zip(bars4, final_capitals_k):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(final_capitals_k)*0.01, 
                f'{value:.0f}K', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存对比图表
    comparison_file = "data/strategy_comparison.png"
    plt.savefig(comparison_file, dpi=300, bbox_inches='tight')
    print(f"📊 策略对比图表已保存: {comparison_file}")
    
    plt.show()
    
    return comparison_file

def load_detailed_results(strategy_dir):
    """加载详细的回测结果数据"""
    
    detailed_file = os.path.join(strategy_dir, "detailed_results.csv")
    if os.path.exists(detailed_file):
        return pd.read_csv(detailed_file)
    return None

def create_equity_curve_comparison(strategies):
    """创建资金曲线对比图"""
    
    print(f"\n📈 创建资金曲线对比图...")
    
    plt.figure(figsize=(15, 10))
    
    colors = ['blue', 'green', 'red', 'orange', 'purple', 'brown', 'pink']
    
    for i, strategy in enumerate(strategies):
        # 加载详细结果
        results_df = load_detailed_results(strategy['dir'])
        
        if results_df is not None and 'capital' in results_df.columns:
            # 转换日期
            if 'date' in results_df.columns:
                results_df['date'] = pd.to_datetime(results_df['date'])
                x_data = results_df['date']
            else:
                x_data = range(len(results_df))
            
            # 绘制资金曲线
            plt.plot(x_data, results_df['capital'], 
                    label=strategy['name'].replace('_', ' ').title(), 
                    linewidth=2, color=colors[i % len(colors)])
            
            # 如果有基准数据，也绘制出来
            if 'benchmark_capital' in results_df.columns and i == 0:  # 只显示第一个策略的基准
                plt.plot(x_data, results_df['benchmark_capital'], 
                        label='等权重基准', linewidth=2, color='gray', alpha=0.7, linestyle='--')
    
    plt.title('策略资金曲线对比', fontsize=16, fontweight='bold')
    plt.xlabel('时间')
    plt.ylabel('资金 (USDT)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 格式化x轴
    if len(strategies) > 0:
        results_df = load_detailed_results(strategies[0]['dir'])
        if results_df is not None and 'date' in results_df.columns and len(results_df) > 100:
            plt.gca().xaxis.set_major_locator(plt.MaxNLocator(10))
    
    plt.tight_layout()
    
    # 保存资金曲线对比图
    equity_file = "data/equity_curves_comparison.png"
    plt.savefig(equity_file, dpi=300, bbox_inches='tight')
    print(f"📈 资金曲线对比图已保存: {equity_file}")
    
    plt.show()
    
    return equity_file

def main():
    """主函数"""
    
    # 1. 展示已有策略
    strategies = show_existing_charts()
    
    if not strategies:
        return
    
    # 2. 创建策略对比图表
    comparison_file = create_summary_chart(strategies)
    
    # 3. 创建资金曲线对比图
    equity_file = create_equity_curve_comparison(strategies)
    
    print(f"\n🎉 图表展示完成！")
    print(f"📁 生成的文件:")
    print(f"  • 策略对比图: data/strategy_comparison.png")
    print(f"  • 资金曲线对比图: data/equity_curves_comparison.png")
    
    # 4. 显示最佳策略
    if len(strategies) > 1:
        best_return = max(strategies, key=lambda x: x['performance'].get('strategy_total_return', 0))
        best_sharpe = max(strategies, key=lambda x: x['performance'].get('sharpe_ratio', 0))
        
        print(f"\n🏆 最佳策略:")
        print(f"  • 最高收益率: {best_return['name']} ({best_return['performance'].get('strategy_total_return', 0):.2%})")
        print(f"  • 最佳夏普比率: {best_sharpe['name']} ({best_sharpe['performance'].get('sharpe_ratio', 0):.3f})")

if __name__ == "__main__":
    main()
