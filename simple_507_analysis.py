#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
507合约每日实体涨跌幅度简化分析工具

功能：
1. 分析507个USDT永续合约的每日实体涨跌幅度
2. 计算实体大小（收盘价与开盘价的差值）
3. 统计涨跌幅度分布
4. 生成分析报告（无图表版本）

作者：加密货币量化交易系统
日期：2025年1月28日
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime
import os
import sys

class Simple507Analyzer:
    """507合约每日实体涨跌幅度简化分析器"""
    
    def __init__(self, data_dir: str = "data"):
        """初始化分析器"""
        self.data_dir = data_dir
        self.contracts_data = None
        
        print("🚀 507合约每日实体涨跌幅度分析器启动")
        print("=" * 60)
    
    def load_contracts_data(self) -> bool:
        """加载507个合约的最新数据"""
        try:
            contracts_file = os.path.join(self.data_dir, "all_perpetual_contracts_last_dates.json")
            
            if not os.path.exists(contracts_file):
                print(f"❌ 未找到合约数据文件: {contracts_file}")
                return False
            
            with open(contracts_file, 'r', encoding='utf-8') as f:
                self.contracts_data = json.load(f)
            
            print(f"✅ 成功加载507合约数据")
            print(f"📊 数据获取时间: {self.contracts_data['fetch_time']}")
            print(f"📈 合约总数: {self.contracts_data['total_symbols']}")
            print(f"✅ 成功获取: {self.contracts_data['successful_count']}")
            print(f"❌ 已下线: {self.contracts_data['delisted_count']}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载合约数据失败: {str(e)}")
            return False
    
    def analyze_daily_ranges(self):
        """分析每日实体涨跌幅度"""
        print("\n📊 开始分析每日实体涨跌幅度...")
        
        results = []
        
        for contract in self.contracts_data['results']:
            symbol = contract['symbol']
            
            # 跳过失败的合约
            if contract['status'] != 'SUCCESS':
                continue
            
            # 获取OHLC数据
            open_price = contract['open']
            high_price = contract['high']
            low_price = contract['low']
            close_price = contract['close']
            volume = contract['volume']
            
            # 计算实体大小（收盘价 - 开盘价）
            body_size = close_price - open_price
            body_size_pct = (body_size / open_price) * 100 if open_price > 0 else 0
            
            # 计算总振幅
            total_range = high_price - low_price
            total_range_pct = (total_range / open_price) * 100 if open_price > 0 else 0
            
            # 判断K线类型
            if body_size > 0:
                candle_type = "阳线"
            elif body_size < 0:
                candle_type = "阴线"
            else:
                candle_type = "十字星"
            
            # 计算实体占总振幅的比例
            body_ratio = abs(body_size) / total_range if total_range > 0 else 0
            
            results.append({
                'symbol': symbol,
                'date': contract['last_date'],
                'open': open_price,
                'close': close_price,
                'high': high_price,
                'low': low_price,
                'volume': volume,
                'body_size': body_size,
                'body_size_pct': body_size_pct,
                'total_range_pct': total_range_pct,
                'candle_type': candle_type,
                'body_ratio': body_ratio
            })
        
        df = pd.DataFrame(results)
        print(f"✅ 成功分析 {len(df)} 个合约的实体涨跌幅度")
        
        return df
    
    def generate_statistics(self, df):
        """生成统计信息"""
        print("\n📈 生成统计信息...")
        
        # 基础统计
        total_contracts = len(df)
        
        # K线类型统计
        candle_counts = df['candle_type'].value_counts()
        yang_count = candle_counts.get('阳线', 0)
        yin_count = candle_counts.get('阴线', 0)
        cross_count = candle_counts.get('十字星', 0)
        
        # 实体大小统计
        body_stats = df['body_size_pct'].describe()
        
        # 振幅统计
        range_stats = df['total_range_pct'].describe()
        
        print(f"\n📊 507合约每日实体涨跌幅度统计报告")
        print(f"数据日期: {df['date'].iloc[0] if len(df) > 0 else 'N/A'}")
        print(f"=" * 60)
        
        print(f"\n🔢 基础统计:")
        print(f"  总合约数: {total_contracts}")
        print(f"  阳线数量: {yang_count} ({yang_count/total_contracts*100:.1f}%)")
        print(f"  阴线数量: {yin_count} ({yin_count/total_contracts*100:.1f}%)")
        print(f"  十字星数量: {cross_count} ({cross_count/total_contracts*100:.1f}%)")
        
        print(f"\n📏 实体大小统计 (%):")
        print(f"  平均值: {body_stats['mean']:.3f}%")
        print(f"  中位数: {body_stats['50%']:.3f}%")
        print(f"  标准差: {body_stats['std']:.3f}%")
        print(f"  最大值: {body_stats['max']:.3f}%")
        print(f"  最小值: {body_stats['min']:.3f}%")
        print(f"  25%分位: {body_stats['25%']:.3f}%")
        print(f"  75%分位: {body_stats['75%']:.3f}%")
        
        print(f"\n📊 总振幅统计 (%):")
        print(f"  平均值: {range_stats['mean']:.3f}%")
        print(f"  中位数: {range_stats['50%']:.3f}%")
        print(f"  标准差: {range_stats['std']:.3f}%")
        print(f"  最大值: {range_stats['max']:.3f}%")
        print(f"  最小值: {range_stats['min']:.3f}%")
        
        # 显示极值合约
        print(f"\n🔝 最大涨幅合约 (Top 10):")
        top_gainers = df.nlargest(10, 'body_size_pct')
        for idx, row in top_gainers.iterrows():
            print(f"  {row['symbol']}: +{row['body_size_pct']:.3f}% (振幅: {row['total_range_pct']:.3f}%)")
        
        print(f"\n🔻 最大跌幅合约 (Top 10):")
        top_losers = df.nsmallest(10, 'body_size_pct')
        for idx, row in top_losers.iterrows():
            print(f"  {row['symbol']}: {row['body_size_pct']:.3f}% (振幅: {row['total_range_pct']:.3f}%)")
        
        print(f"\n📈 最大振幅合约 (Top 10):")
        top_ranges = df.nlargest(10, 'total_range_pct')
        for idx, row in top_ranges.iterrows():
            print(f"  {row['symbol']}: {row['total_range_pct']:.3f}% (实体: {row['body_size_pct']:.3f}%)")
        
        return {
            'total_contracts': total_contracts,
            'yang_count': yang_count,
            'yin_count': yin_count,
            'cross_count': cross_count,
            'body_stats': body_stats,
            'range_stats': range_stats,
            'top_gainers': top_gainers,
            'top_losers': top_losers,
            'top_ranges': top_ranges
        }
    
    def save_detailed_data(self, df):
        """保存详细数据"""
        try:
            # 保存CSV文件
            csv_file = f"507_contracts_daily_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"\n💾 详细数据已保存: {csv_file}")
            
            # 保存JSON文件
            json_file = f"507_contracts_daily_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            analysis_data = {
                'analysis_time': datetime.now().isoformat(),
                'data_source': self.contracts_data['data_source'],
                'total_contracts': len(df),
                'data_date': df['date'].iloc[0] if len(df) > 0 else None,
                'contracts': df.to_dict('records')
            }
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 JSON数据已保存: {json_file}")
            
            return csv_file, json_file
            
        except Exception as e:
            print(f"❌ 保存数据失败: {str(e)}")
            return None, None
    
    def run_analysis(self):
        """运行完整分析"""
        try:
            # 1. 加载数据
            if not self.load_contracts_data():
                return False
            
            # 2. 分析实体涨跌幅度
            df = self.analyze_daily_ranges()
            if df.empty:
                print("❌ 没有有效的合约数据")
                return False
            
            # 3. 生成统计信息
            stats = self.generate_statistics(df)
            
            # 4. 保存详细数据
            csv_file, json_file = self.save_detailed_data(df)
            
            print(f"\n🎉 507合约每日实体涨跌幅度分析完成！")
            print(f"📊 分析了 {len(df)} 个合约")
            print(f"📅 数据日期: {df['date'].iloc[0] if len(df) > 0 else 'N/A'}")
            
            if csv_file:
                print(f"📋 详细数据: {csv_file}")
            if json_file:
                print(f"📋 JSON数据: {json_file}")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    print("🚀 启动507合约每日实体涨跌幅度分析...")
    
    # 创建分析器
    analyzer = Simple507Analyzer()
    
    # 运行分析
    success = analyzer.run_analysis()
    
    if success:
        print("\n✅ 分析完成！")
    else:
        print("\n❌ 分析失败！")
    
    print("\n按回车键退出...")
    input()
    
    return success

if __name__ == "__main__":
    main()
