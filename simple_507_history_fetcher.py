#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
507合约全量历史数据获取工具（简化版）

功能：
1. 获取507个USDT永续合约的完整历史数据
2. 从各合约上线开始到最新的全量数据
3. 简化版本，专注于稳定性和可靠性
4. 实时进度显示和错误处理
5. 100%使用币安官方API数据

作者：加密货币量化交易系统
日期：2025年1月28日
"""

import requests
import pandas as pd
import json
import os
import time
import sys
from datetime import datetime
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

class Simple507HistoryFetcher:
    """507合约全量历史数据获取器（简化版）"""
    
    def __init__(self):
        """初始化数据获取器"""
        self.base_url = "https://fapi.binance.com/fapi/v1"
        self.data_dir = "perpetual_historical_data"
        
        # 创建数据目录
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 设置请求会话
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        print("🚀 507合约全量历史数据获取器启动")
        print("=" * 50)
        print(f"📁 数据目录: {self.data_dir}")
        print(f"🌐 API端点: {self.base_url}")
        print(f"🔒 数据来源: 币安官方期货API")
    
    def load_contracts_list(self) -> List[str]:
        """加载507个合约列表"""
        try:
            print("\n📋 加载507个合约列表...")
            
            # 从之前的分析文件中加载
            contracts_file = "data/all_perpetual_contracts_last_dates.json"
            
            if not os.path.exists(contracts_file):
                print(f"❌ 未找到合约列表文件: {contracts_file}")
                return []
            
            with open(contracts_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            symbols = []
            for contract in data['results']:
                if contract['status'] == 'SUCCESS':
                    symbols.append(contract['symbol'])
            
            print(f"✅ 成功加载 {len(symbols)} 个合约")
            return symbols
            
        except Exception as e:
            print(f"❌ 加载合约列表失败: {str(e)}")
            return []
    
    def get_klines_data(self, symbol: str, limit: int = 1500, end_time: Optional[int] = None) -> List:
        """
        获取K线数据
        
        Args:
            symbol: 合约符号
            limit: 数据条数限制
            end_time: 结束时间戳
            
        Returns:
            List: K线数据列表
        """
        try:
            url = f"{self.base_url}/klines"
            params = {
                'symbol': symbol,
                'interval': '1d',
                'limit': limit
            }
            
            if end_time:
                params['endTime'] = end_time
            
            response = self.session.get(url, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ {symbol} API请求失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ {symbol} 请求异常: {str(e)}")
            return []
    
    def fetch_full_history(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        获取单个合约的完整历史数据
        
        Args:
            symbol: 合约符号
            
        Returns:
            Optional[pd.DataFrame]: 历史数据或None
        """
        try:
            print(f"📊 获取 {symbol} 完整历史数据...")
            
            all_data = []
            end_time = None
            batch_count = 0
            
            while True:
                # 获取一批数据
                klines = self.get_klines_data(symbol, limit=1500, end_time=end_time)
                
                if not klines:
                    break
                
                all_data.extend(klines)
                batch_count += 1
                
                print(f"  📈 {symbol}: 第{batch_count}批，累计 {len(all_data)} 条数据")
                
                # 检查是否获取完所有数据
                if len(klines) < 1500:
                    break
                
                # 设置下一批的结束时间
                end_time = klines[0][0] - 1
                
                # 速率控制
                time.sleep(1.2)
                
                # 防止无限循环
                if batch_count > 100:  # 最多100批，约15万条数据
                    print(f"⚠️ {symbol}: 达到最大批次限制")
                    break
            
            if not all_data:
                print(f"❌ {symbol}: 未获取到数据")
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(all_data, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'count', 'taker_buy_volume',
                'taker_buy_quote_volume', 'ignore'
            ])
            
            # 数据处理
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['date'] = df['timestamp'].dt.date
            
            # 数据类型转换
            for col in ['open', 'high', 'low', 'close', 'volume', 'quote_volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 排序和去重
            df = df.sort_values('timestamp').drop_duplicates(subset=['timestamp']).reset_index(drop=True)
            
            print(f"✅ {symbol}: 成功获取 {len(df)} 条历史数据")
            print(f"  📅 时间范围: {df['date'].min()} 至 {df['date'].max()}")
            
            return df
            
        except Exception as e:
            print(f"❌ {symbol} 获取历史数据失败: {str(e)}")
            return None
    
    def save_data(self, df: pd.DataFrame, symbol: str) -> bool:
        """
        保存数据到文件
        
        Args:
            df: 数据框
            symbol: 合约符号
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 保存CSV文件
            csv_file = os.path.join(self.data_dir, f"{symbol}_history.csv")
            df_save = df[['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume', 'quote_volume']].copy()
            df_save.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            # 保存元数据
            metadata = {
                'symbol': symbol,
                'download_time': datetime.now().isoformat(),
                'data_points': len(df),
                'date_range': {
                    'start': str(df['date'].min()),
                    'end': str(df['date'].max())
                },
                'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
                'api_endpoint': f"{self.base_url}/klines",
                'file_path': csv_file
            }
            
            # 数据分级
            days_count = len(df)
            if days_count >= 365:
                metadata['data_grade'] = 'A'
            elif days_count >= 30:
                metadata['data_grade'] = 'B'
            elif days_count >= 7:
                metadata['data_grade'] = 'C'
            else:
                metadata['data_grade'] = 'D'
            
            # 保存元数据
            metadata_file = os.path.join(self.data_dir, f"{symbol}_metadata.json")
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            print(f"💾 {symbol}: 数据已保存 (等级: {metadata['data_grade']})")
            
            return True
            
        except Exception as e:
            print(f"❌ {symbol} 保存数据失败: {str(e)}")
            return False
    
    def run_batch_download(self, start_index: int = 0, batch_size: int = 10) -> Dict:
        """
        批量下载数据
        
        Args:
            start_index: 开始索引
            batch_size: 批次大小
            
        Returns:
            Dict: 下载结果统计
        """
        try:
            # 加载合约列表
            symbols = self.load_contracts_list()
            if not symbols:
                return {'success': 0, 'failed': 0, 'total': 0}
            
            # 确定下载范围
            end_index = min(start_index + batch_size, len(symbols))
            batch_symbols = symbols[start_index:end_index]
            
            print(f"\n🚀 开始批量下载 ({start_index+1}-{end_index}/{len(symbols)})")
            print("=" * 50)
            
            success_count = 0
            failed_count = 0
            results = []
            
            for i, symbol in enumerate(batch_symbols, 1):
                print(f"\n[{start_index + i}/{len(symbols)}] 处理 {symbol}...")
                
                # 检查是否已存在
                csv_file = os.path.join(self.data_dir, f"{symbol}_history.csv")
                if os.path.exists(csv_file):
                    print(f"⏭️ {symbol}: 数据已存在，跳过")
                    success_count += 1
                    continue
                
                # 获取历史数据
                df = self.fetch_full_history(symbol)
                
                if df is not None and len(df) > 0:
                    # 保存数据
                    if self.save_data(df, symbol):
                        success_count += 1
                        results.append({
                            'symbol': symbol,
                            'status': 'success',
                            'data_points': len(df),
                            'date_range': f"{df['date'].min()} - {df['date'].max()}"
                        })
                    else:
                        failed_count += 1
                        results.append({'symbol': symbol, 'status': 'save_failed'})
                else:
                    failed_count += 1
                    results.append({'symbol': symbol, 'status': 'fetch_failed'})
                
                # 显示进度
                total_progress = (start_index + i) / len(symbols) * 100
                batch_progress = i / len(batch_symbols) * 100
                print(f"📈 批次进度: {batch_progress:.1f}% | 总进度: {total_progress:.1f}%")
                
                # 速率控制
                time.sleep(1.5)
            
            # 保存批次结果
            batch_result = {
                'batch_info': {
                    'start_index': start_index,
                    'end_index': end_index,
                    'batch_size': len(batch_symbols),
                    'download_time': datetime.now().isoformat()
                },
                'statistics': {
                    'success': success_count,
                    'failed': failed_count,
                    'total': len(batch_symbols),
                    'success_rate': success_count / len(batch_symbols) * 100 if batch_symbols else 0
                },
                'results': results
            }
            
            # 保存批次报告
            batch_file = os.path.join(self.data_dir, f"batch_report_{start_index}_{end_index}.json")
            with open(batch_file, 'w', encoding='utf-8') as f:
                json.dump(batch_result, f, ensure_ascii=False, indent=2)
            
            print(f"\n📊 批次 {start_index+1}-{end_index} 完成:")
            print(f"✅ 成功: {success_count}")
            print(f"❌ 失败: {failed_count}")
            print(f"📈 成功率: {success_count/len(batch_symbols)*100:.1f}%")
            print(f"📝 报告: {batch_file}")
            
            return batch_result['statistics']
            
        except Exception as e:
            print(f"❌ 批量下载失败: {str(e)}")
            return {'success': 0, 'failed': 0, 'total': 0}
    
    def show_download_menu(self):
        """显示下载菜单"""
        print("\n📋 507合约历史数据下载选项:")
        print("1. 下载前10个合约（测试）")
        print("2. 下载前50个合约")
        print("3. 下载前100个合约")
        print("4. 下载全部507个合约")
        print("5. 自定义范围下载")
        print("6. 查看已下载状态")
        print("0. 退出")
        
        choice = input("\n请选择操作 (0-6): ").strip()
        
        if choice == '1':
            self.run_batch_download(0, 10)
        elif choice == '2':
            self.run_batch_download(0, 50)
        elif choice == '3':
            self.run_batch_download(0, 100)
        elif choice == '4':
            self.download_all_contracts()
        elif choice == '5':
            self.custom_range_download()
        elif choice == '6':
            self.show_download_status()
        elif choice == '0':
            print("👋 退出程序")
            return False
        else:
            print("❌ 无效选择，请重新输入")
        
        return True
    
    def download_all_contracts(self):
        """下载全部合约"""
        print("\n🚀 开始下载全部507个合约...")
        
        symbols = self.load_contracts_list()
        if not symbols:
            return
        
        batch_size = 20  # 每批20个
        total_batches = (len(symbols) + batch_size - 1) // batch_size
        
        print(f"📊 将分 {total_batches} 批下载，每批 {batch_size} 个合约")
        
        confirm = input("确认开始下载？(y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 取消下载")
            return
        
        total_success = 0
        total_failed = 0
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            print(f"\n🔄 执行第 {batch_num + 1}/{total_batches} 批...")
            
            result = self.run_batch_download(start_idx, batch_size)
            total_success += result['success']
            total_failed += result['failed']
            
            print(f"📈 总体进度: {(batch_num + 1)/total_batches*100:.1f}%")
            
            # 批次间休息
            if batch_num < total_batches - 1:
                print("⏸️ 批次间休息 10 秒...")
                time.sleep(10)
        
        print(f"\n🎉 全部下载完成!")
        print(f"✅ 总成功: {total_success}")
        print(f"❌ 总失败: {total_failed}")
        print(f"📈 总成功率: {total_success/(total_success+total_failed)*100:.1f}%")
    
    def custom_range_download(self):
        """自定义范围下载"""
        symbols = self.load_contracts_list()
        if not symbols:
            return
        
        print(f"\n📊 共有 {len(symbols)} 个合约")
        
        try:
            start = int(input("请输入开始索引 (1开始): ")) - 1
            end = int(input("请输入结束索引: "))
            
            if start < 0 or end > len(symbols) or start >= end:
                print("❌ 索引范围无效")
                return
            
            batch_size = end - start
            print(f"📋 将下载第 {start+1} 到第 {end} 个合约，共 {batch_size} 个")
            
            confirm = input("确认下载？(y/N): ").strip().lower()
            if confirm == 'y':
                self.run_batch_download(start, batch_size)
            
        except ValueError:
            print("❌ 请输入有效的数字")
    
    def show_download_status(self):
        """显示下载状态"""
        symbols = self.load_contracts_list()
        if not symbols:
            return
        
        downloaded = 0
        for symbol in symbols:
            csv_file = os.path.join(self.data_dir, f"{symbol}_history.csv")
            if os.path.exists(csv_file):
                downloaded += 1
        
        print(f"\n📊 下载状态统计:")
        print(f"✅ 已下载: {downloaded} 个")
        print(f"⏳ 未下载: {len(symbols) - downloaded} 个")
        print(f"📈 完成率: {downloaded/len(symbols)*100:.1f}%")
        print(f"📁 数据目录: {self.data_dir}")

def main():
    """主函数"""
    print("🚀 启动507合约全量历史数据获取工具...")
    
    fetcher = Simple507HistoryFetcher()
    
    while True:
        if not fetcher.show_download_menu():
            break
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
