# -*- coding: utf-8 -*-
"""
比特币和以太坊简单选优策略：基于RSI和移动平均线选择表现更好的币种
展示资金曲线图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
import os
import json

# 设置中文字体和忽略警告
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

class BTCETHSimpleStrategy:
    """BTC和ETH简单选优策略"""
    
    def __init__(self):
        """初始化策略"""
        self.data_dir = "data/btc_eth_data"
        self.results_dir = "data/btc_eth_strategy_results"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 策略参数
        self.initial_capital = 100000  # 初始资金10万USDT
        self.transaction_cost = 0.001  # 交易费用0.1%
        
    def load_data(self):
        """加载BTC和ETH数据"""
        print("📊 加载比特币和以太坊历史数据...")
        
        # 加载合并数据
        combined_file = os.path.join(self.data_dir, "btc_eth_combined.csv")
        df = pd.read_csv(combined_file)
        df['date'] = pd.to_datetime(df['date'])
        
        print(f"✅ 数据加载完成:")
        print(f"  • 总记录数: {len(df):,} 条")
        print(f"  • 日期范围: {df['date'].min()} 到 {df['date'].max()}")
        print(f"  • BTC记录: {len(df[df['symbol']=='BTCUSDT']):,} 条")
        print(f"  • ETH记录: {len(df[df['symbol']=='ETHUSDT']):,} 条")
        
        return df
    
    def create_indicators(self, df):
        """创建技术指标"""
        print("🔧 创建技术指标...")
        
        indicators_df = []
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_df = df[df['symbol'] == symbol].copy().sort_values('date').reset_index(drop=True)
            
            # 基础价格特征
            symbol_df['returns'] = symbol_df['close'].pct_change()
            
            # 移动平均线
            symbol_df['ma_5'] = symbol_df['close'].rolling(5).mean()
            symbol_df['ma_20'] = symbol_df['close'].rolling(20).mean()
            symbol_df['ma_50'] = symbol_df['close'].rolling(50).mean()
            
            # 移动平均线比率
            symbol_df['ma_ratio_5_20'] = symbol_df['ma_5'] / symbol_df['ma_20']
            symbol_df['ma_ratio_20_50'] = symbol_df['ma_20'] / symbol_df['ma_50']
            
            # RSI
            delta = symbol_df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            symbol_df['rsi'] = 100 - (100 / (1 + rs))
            
            # 价格相对位置
            symbol_df['price_position'] = (symbol_df['close'] - symbol_df['close'].rolling(20).min()) / \
                                        (symbol_df['close'].rolling(20).max() - symbol_df['close'].rolling(20).min())
            
            # 成交量比率
            symbol_df['volume_ratio'] = symbol_df['volume'] / symbol_df['volume'].rolling(20).mean()
            
            # 波动率
            symbol_df['volatility'] = symbol_df['returns'].rolling(20).std()
            
            # 动量指标
            symbol_df['momentum_5'] = symbol_df['close'] / symbol_df['close'].shift(5) - 1
            symbol_df['momentum_10'] = symbol_df['close'] / symbol_df['close'].shift(10) - 1
            
            # 综合评分（简单加权）
            symbol_df['trend_score'] = (
                (symbol_df['ma_ratio_5_20'] - 1) * 2 +  # 短期趋势
                (symbol_df['ma_ratio_20_50'] - 1) * 1 +  # 中期趋势
                (symbol_df['rsi'] - 50) / 50 * 0.5 +     # RSI偏离中性
                symbol_df['momentum_5'] * 1 +            # 短期动量
                symbol_df['momentum_10'] * 0.5           # 中期动量
            )
            
            indicators_df.append(symbol_df)
        
        # 合并数据
        combined_indicators = pd.concat(indicators_df, ignore_index=True)
        combined_indicators = combined_indicators.sort_values(['symbol', 'date']).reset_index(drop=True)
        
        print(f"✅ 技术指标创建完成")
        
        return combined_indicators
    
    def backtest_strategy(self, df):
        """回测策略"""
        print("📊 执行策略回测...")
        
        # 获取共同的日期
        btc_data = df[df['symbol'] == 'BTCUSDT'].set_index('date')
        eth_data = df[df['symbol'] == 'ETHUSDT'].set_index('date')
        
        # 找到共同日期
        common_dates = btc_data.index.intersection(eth_data.index)
        common_dates = sorted(common_dates)
        
        print(f"📅 共同交易日期: {len(common_dates)} 天")
        
        strategy_results = []
        capital = self.initial_capital
        benchmark_capital = self.initial_capital
        position = None
        
        for i, date in enumerate(common_dates):
            if i == 0:  # 跳过第一天
                continue
                
            try:
                # 获取当日数据
                btc_today = btc_data.loc[date]
                eth_today = eth_data.loc[date]
                
                # 获取昨日数据用于计算收益
                prev_date = common_dates[i-1]
                btc_prev = btc_data.loc[prev_date]
                eth_prev = eth_data.loc[prev_date]
                
                # 计算实际收益率
                btc_return = (btc_today['close'] - btc_prev['close']) / btc_prev['close']
                eth_return = (eth_today['close'] - eth_prev['close']) / eth_prev['close']
                
                # 策略逻辑：选择综合评分更高的币种
                btc_score = btc_prev['trend_score'] if not pd.isna(btc_prev['trend_score']) else 0
                eth_score = eth_prev['trend_score'] if not pd.isna(eth_prev['trend_score']) else 0
                
                # 选择评分更高的币种，但需要评分大于阈值
                if btc_score > eth_score and btc_score > 0.01:
                    new_position = 'BTCUSDT'
                    selected_return = btc_return
                elif eth_score > btc_score and eth_score > 0.01:
                    new_position = 'ETHUSDT'
                    selected_return = eth_return
                else:
                    new_position = None  # 空仓
                    selected_return = 0
                
                # 计算策略收益
                if new_position is not None:
                    if position != new_position:
                        # 换仓或开仓，扣除交易费用
                        net_return = selected_return - self.transaction_cost
                        if position is not None:  # 换仓额外成本
                            net_return -= self.transaction_cost * 0.5
                    else:
                        # 继续持有，只扣除持有成本
                        net_return = selected_return - self.transaction_cost * 0.1
                    
                    capital *= (1 + net_return)
                else:
                    # 空仓，无收益但也无成本
                    pass
                
                # 更新持仓
                position = new_position
                
                # 计算基准收益（等权重）
                benchmark_return = (btc_return + eth_return) / 2
                benchmark_capital *= (1 + benchmark_return - self.transaction_cost * 0.1)
                
                strategy_results.append({
                    'date': date,
                    'capital': capital,
                    'benchmark_capital': benchmark_capital,
                    'position': position,
                    'btc_score': btc_score,
                    'eth_score': eth_score,
                    'btc_return': btc_return,
                    'eth_return': eth_return,
                    'selected_return': selected_return,
                    'btc_price': btc_today['close'],
                    'eth_price': eth_today['close'],
                    'btc_rsi': btc_today['rsi'],
                    'eth_rsi': eth_today['rsi']
                })
                
            except Exception as e:
                print(f"⚠️ 处理日期 {date} 时出错: {e}")
                continue
        
        strategy_df = pd.DataFrame(strategy_results)
        
        print(f"✅ 回测完成，共 {len(strategy_df)} 个交易日")
        
        return strategy_df
    
    def analyze_performance(self, results_df):
        """分析策略表现"""
        print("📈 分析策略表现...")
        
        # 计算收益率
        results_df['strategy_returns'] = results_df['capital'].pct_change()
        results_df['benchmark_returns'] = results_df['benchmark_capital'].pct_change()
        
        # 计算累计收益
        strategy_total_return = (results_df['capital'].iloc[-1] / self.initial_capital) - 1
        benchmark_total_return = (results_df['benchmark_capital'].iloc[-1] / self.initial_capital) - 1
        
        # 计算年化收益率
        days = len(results_df)
        years = days / 365
        strategy_annual_return = (1 + strategy_total_return) ** (1 / years) - 1
        benchmark_annual_return = (1 + benchmark_total_return) ** (1 / years) - 1
        
        # 计算最大回撤
        results_df['strategy_peak'] = results_df['capital'].cummax()
        results_df['strategy_drawdown'] = (results_df['capital'] - results_df['strategy_peak']) / results_df['strategy_peak']
        max_drawdown = results_df['strategy_drawdown'].min()
        
        results_df['benchmark_peak'] = results_df['benchmark_capital'].cummax()
        results_df['benchmark_drawdown'] = (results_df['benchmark_capital'] - results_df['benchmark_peak']) / results_df['benchmark_peak']
        benchmark_max_drawdown = results_df['benchmark_drawdown'].min()
        
        # 计算夏普比率
        strategy_sharpe = results_df['strategy_returns'].mean() / results_df['strategy_returns'].std() * np.sqrt(252) if results_df['strategy_returns'].std() > 0 else 0
        benchmark_sharpe = results_df['benchmark_returns'].mean() / results_df['benchmark_returns'].std() * np.sqrt(252) if results_df['benchmark_returns'].std() > 0 else 0
        
        # 计算胜率
        win_rate = (results_df['strategy_returns'] > 0).mean()
        
        # 持仓统计
        position_stats = results_df['position'].value_counts()
        
        performance = {
            'strategy_total_return': strategy_total_return,
            'benchmark_total_return': benchmark_total_return,
            'strategy_annual_return': strategy_annual_return,
            'benchmark_annual_return': benchmark_annual_return,
            'excess_return': strategy_total_return - benchmark_total_return,
            'max_drawdown': max_drawdown,
            'benchmark_max_drawdown': benchmark_max_drawdown,
            'strategy_sharpe': strategy_sharpe,
            'benchmark_sharpe': benchmark_sharpe,
            'win_rate': win_rate,
            'total_trades': len(results_df),
            'final_capital': results_df['capital'].iloc[-1],
            'benchmark_final_capital': results_df['benchmark_capital'].iloc[-1],
            'position_stats': position_stats.to_dict(),
            'years': years
        }
        
        return performance, results_df
    
    def plot_results(self, results_df, performance):
        """绘制结果图表"""
        print("📊 生成结果图表...")
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('BTC-ETH简单选优策略回测结果', fontsize=16, fontweight='bold')
        
        # 1. 资金曲线图
        ax1 = axes[0, 0]
        ax1.plot(results_df['date'], results_df['capital'], label='选优策略', linewidth=2, color='blue')
        ax1.plot(results_df['date'], results_df['benchmark_capital'], label='等权重基准', linewidth=2, color='red', alpha=0.7)
        ax1.set_title('资金曲线对比', fontsize=14, fontweight='bold')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('资金 (USDT)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 格式化日期
        if len(results_df) > 100:
            ax1.xaxis.set_major_locator(plt.MaxNLocator(10))
        
        # 添加收益率标注
        final_strategy_return = performance['strategy_total_return']
        final_benchmark_return = performance['benchmark_total_return']
        ax1.text(0.02, 0.98, f'策略收益: {final_strategy_return:.2%}', transform=ax1.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        ax1.text(0.02, 0.90, f'基准收益: {final_benchmark_return:.2%}', transform=ax1.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
        
        # 2. 回撤图
        ax2 = axes[0, 1]
        ax2.fill_between(results_df['date'], results_df['strategy_drawdown'], 0, 
                        alpha=0.3, color='red', label='策略回撤')
        ax2.fill_between(results_df['date'], results_df['benchmark_drawdown'], 0, 
                        alpha=0.3, color='orange', label='基准回撤')
        ax2.set_title('回撤分析', fontsize=14, fontweight='bold')
        ax2.set_xlabel('日期')
        ax2.set_ylabel('回撤比例')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        if len(results_df) > 100:
            ax2.xaxis.set_major_locator(plt.MaxNLocator(10))
        
        # 3. 持仓分布
        ax3 = axes[1, 0]
        position_counts = results_df['position'].value_counts()
        position_counts = position_counts[position_counts.index.notna()]  # 移除空仓
        if len(position_counts) > 0:
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
            wedges, texts, autotexts = ax3.pie(position_counts.values, labels=position_counts.index, 
                                              autopct='%1.1f%%', colors=colors[:len(position_counts)])
            ax3.set_title('持仓分布', fontsize=14, fontweight='bold')
        
        # 4. 价格走势对比
        ax4 = axes[1, 1]
        
        # 计算归一化价格（以第一天为基准）
        btc_normalized = results_df['btc_price'] / results_df['btc_price'].iloc[0]
        eth_normalized = results_df['eth_price'] / results_df['eth_price'].iloc[0]
        strategy_normalized = results_df['capital'] / results_df['capital'].iloc[0]
        
        ax4.plot(results_df['date'], btc_normalized, label='BTC价格', linewidth=1, alpha=0.7, color='orange')
        ax4.plot(results_df['date'], eth_normalized, label='ETH价格', linewidth=1, alpha=0.7, color='purple')
        ax4.plot(results_df['date'], strategy_normalized, label='策略净值', linewidth=2, color='blue')
        
        ax4.set_title('归一化价格走势对比', fontsize=14, fontweight='bold')
        ax4.set_xlabel('日期')
        ax4.set_ylabel('归一化价格')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        if len(results_df) > 100:
            ax4.xaxis.set_major_locator(plt.MaxNLocator(10))
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = os.path.join(self.results_dir, 'btc_eth_strategy_results.png')
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {chart_file}")
        
        plt.show()
        
        return chart_file
    
    def save_results(self, results_df, performance):
        """保存结果"""
        print("💾 保存回测结果...")
        
        # 保存详细结果
        results_file = os.path.join(self.results_dir, 'detailed_results.csv')
        results_df.to_csv(results_file, index=False, encoding='utf-8-sig')
        
        # 保存性能指标
        performance_file = os.path.join(self.results_dir, 'performance_metrics.json')
        with open(performance_file, 'w', encoding='utf-8') as f:
            json.dump(performance, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✅ 结果已保存:")
        print(f"  • 详细结果: {results_file}")
        print(f"  • 性能指标: {performance_file}")
    
    def run(self):
        """执行完整的策略回测流程"""
        print("🚀 开始BTC-ETH简单选优策略回测...")
        print("=" * 80)
        
        try:
            # 1. 加载数据
            df = self.load_data()
            
            # 2. 创建技术指标
            indicators_df = self.create_indicators(df)
            
            # 3. 回测策略
            results_df = self.backtest_strategy(indicators_df)
            
            # 4. 分析表现
            performance, results_df = self.analyze_performance(results_df)
            
            # 5. 绘制图表
            chart_file = self.plot_results(results_df, performance)
            
            # 6. 保存结果
            self.save_results(results_df, performance)
            
            # 7. 显示总结
            print("\n🎉 BTC-ETH选优策略回测完成！")
            print("=" * 80)
            print(f"📈 策略表现总结:")
            print(f"  • 回测期间: {performance['years']:.1f} 年")
            print(f"  • 策略总收益率: {performance['strategy_total_return']:.2%}")
            print(f"  • 基准总收益率: {performance['benchmark_total_return']:.2%}")
            print(f"  • 超额收益: {performance['excess_return']:.2%}")
            print(f"  • 策略年化收益率: {performance['strategy_annual_return']:.2%}")
            print(f"  • 基准年化收益率: {performance['benchmark_annual_return']:.2%}")
            print(f"  • 最大回撤: {performance['max_drawdown']:.2%}")
            print(f"  • 夏普比率: {performance['strategy_sharpe']:.3f}")
            print(f"  • 胜率: {performance['win_rate']:.2%}")
            print(f"  • 最终资金: {performance['final_capital']:,.0f} USDT")
            
            # 持仓分布
            print(f"\n📊 持仓分布:")
            for position, count in performance['position_stats'].items():
                if position is not None:
                    percentage = count / performance['total_trades'] * 100
                    print(f"  • {position}: {count} 天 ({percentage:.1f}%)")
            
            print(f"📁 结果保存在: {self.results_dir}")
            
            return True
            
        except Exception as e:
            print(f"❌ 策略回测失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    strategy = BTCETHSimpleStrategy()
    success = strategy.run()
    
    if success:
        print("\n✅ 程序执行成功")
    else:
        print("\n❌ 程序执行失败")

if __name__ == "__main__":
    main()
