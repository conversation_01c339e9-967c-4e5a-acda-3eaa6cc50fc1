# -*- coding: utf-8 -*-
"""
提取比特币和以太坊USDT永续合约的全量历史数据
"""

import pandas as pd
import os
import shutil
from datetime import datetime

def extract_btc_eth_data():
    """提取BTC和ETH的历史数据"""
    
    # 源数据目录
    source_dir = "data/perpetual_historical"
    
    # 目标目录
    target_dir = "data/btc_eth_data"
    os.makedirs(target_dir, exist_ok=True)
    
    # 要提取的合约
    contracts = ['BTCUSDT', 'ETHUSDT']
    
    print("🚀 开始提取比特币和以太坊历史数据...")
    
    for contract in contracts:
        # 源文件路径
        source_csv = os.path.join(source_dir, f"{contract}_historical.csv")
        source_parquet = os.path.join(source_dir, f"{contract}_historical.parquet")
        
        # 目标文件路径
        target_csv = os.path.join(target_dir, f"{contract}_historical.csv")
        target_parquet = os.path.join(target_dir, f"{contract}_historical.parquet")
        
        if os.path.exists(source_csv):
            # 复制CSV文件
            shutil.copy2(source_csv, target_csv)
            print(f"✅ 复制 {contract} CSV文件完成")
            
            # 读取并显示数据信息
            df = pd.read_csv(source_csv)
            print(f"📊 {contract} 数据信息:")
            print(f"  • 记录数: {len(df):,} 条")
            print(f"  • 开始日期: {df['date'].iloc[0]}")
            print(f"  • 结束日期: {df['date'].iloc[-1]}")
            print(f"  • 价格范围: ${df['low'].min():.2f} - ${df['high'].max():.2f}")
            print(f"  • 平均成交量: {df['volume'].mean():,.0f}")
            
        if os.path.exists(source_parquet):
            # 复制Parquet文件
            shutil.copy2(source_parquet, target_parquet)
            print(f"✅ 复制 {contract} Parquet文件完成")
        
        print()
    
    # 生成合并数据文件
    print("📊 生成合并数据文件...")
    
    combined_data = []
    for contract in contracts:
        csv_file = os.path.join(target_dir, f"{contract}_historical.csv")
        if os.path.exists(csv_file):
            df = pd.read_csv(csv_file)
            df['symbol'] = contract
            combined_data.append(df)
    
    if combined_data:
        # 合并数据
        combined_df = pd.concat(combined_data, ignore_index=True)
        combined_df = combined_df.sort_values(['symbol', 'date']).reset_index(drop=True)
        
        # 保存合并数据
        combined_file = os.path.join(target_dir, "btc_eth_combined.csv")
        combined_df.to_csv(combined_file, index=False, encoding='utf-8-sig')
        
        print(f"✅ 合并数据保存完成: {combined_file}")
        print(f"📊 合并数据信息:")
        print(f"  • 总记录数: {len(combined_df):,} 条")
        print(f"  • BTC记录数: {len(combined_df[combined_df['symbol']=='BTCUSDT']):,} 条")
        print(f"  • ETH记录数: {len(combined_df[combined_df['symbol']=='ETHUSDT']):,} 条")
    
    # 生成数据摘要
    summary = {
        'extraction_time': datetime.now().isoformat(),
        'contracts': contracts,
        'source_directory': source_dir,
        'target_directory': target_dir,
        'files_created': []
    }
    
    for contract in contracts:
        csv_file = os.path.join(target_dir, f"{contract}_historical.csv")
        if os.path.exists(csv_file):
            df = pd.read_csv(csv_file)
            summary['files_created'].append({
                'contract': contract,
                'file': f"{contract}_historical.csv",
                'records': len(df),
                'start_date': df['date'].iloc[0],
                'end_date': df['date'].iloc[-1],
                'file_size_mb': os.path.getsize(csv_file) / (1024*1024)
            })
    
    # 保存摘要
    import json
    summary_file = os.path.join(target_dir, "extraction_summary.json")
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"📁 数据摘要保存: {summary_file}")
    print("🎉 比特币和以太坊数据提取完成！")
    
    return target_dir

if __name__ == "__main__":
    extract_btc_eth_data()
