# -*- coding: utf-8 -*-
"""
BTC-ETH选优策略回测结果总结
"""

import json
import os

def print_strategy_summary():
    """打印策略回测结果总结"""
    
    results_dir = "data/btc_eth_strategy_results"
    
    # 读取性能指标
    with open(os.path.join(results_dir, "performance_metrics.json"), 'r', encoding='utf-8') as f:
        performance = json.load(f)
    
    print("🎉 BTC-ETH简单选优策略回测结果总结")
    print("=" * 80)
    
    # 基本信息
    print(f"📊 回测基本信息:")
    print(f"  • 回测期间: {performance['years']:.1f} 年 (2019年11月 - 2025年7月)")
    print(f"  • 交易天数: {performance['total_trades']:,} 天")
    print(f"  • 初始资金: 100,000 USDT")
    print(f"  • 交易费用: 0.1%")
    print(f"  • 策略逻辑: 基于技术指标综合评分选择BTC或ETH")
    
    # 收益率分析
    print(f"\n💰 收益率表现:")
    print(f"  • 策略总收益率: {performance['strategy_total_return']:.2%}")
    print(f"  • 基准总收益率: {performance['benchmark_total_return']:.2%}")
    print(f"  • 超额收益: {performance['excess_return']:.2%}")
    print(f"  • 策略年化收益率: {performance['strategy_annual_return']:.2%}")
    print(f"  • 基准年化收益率: {performance['benchmark_annual_return']:.2%}")
    
    # 风险指标
    print(f"\n⚠️ 风险控制指标:")
    print(f"  • 策略最大回撤: {performance['max_drawdown']:.2%}")
    print(f"  • 基准最大回撤: {performance['benchmark_max_drawdown']:.2%}")
    print(f"  • 策略夏普比率: {performance['strategy_sharpe']:.3f}")
    print(f"  • 基准夏普比率: {performance['benchmark_sharpe']:.3f}")
    print(f"  • 策略胜率: {performance['win_rate']:.2%}")
    
    # 最终资金
    print(f"\n💵 资金表现:")
    print(f"  • 策略最终资金: {performance['final_capital']:,.0f} USDT")
    print(f"  • 基准最终资金: {performance['benchmark_final_capital']:,.0f} USDT")
    print(f"  • 绝对收益差额: {performance['final_capital'] - performance['benchmark_final_capital']:,.0f} USDT")
    
    # 持仓分布
    print(f"\n📊 持仓分布:")
    total_days = sum(performance['position_stats'].values())
    for position, days in performance['position_stats'].items():
        percentage = days / total_days * 100
        print(f"  • {position}: {days:,} 天 ({percentage:.1f}%)")
    
    # 策略优势分析
    print(f"\n🌟 策略优势:")
    advantages = []
    disadvantages = []
    
    if performance['strategy_sharpe'] > performance['benchmark_sharpe']:
        advantages.append(f"夏普比率更优: {performance['strategy_sharpe']:.3f} vs {performance['benchmark_sharpe']:.3f}")
    else:
        disadvantages.append(f"夏普比率较低: {performance['strategy_sharpe']:.3f} vs {performance['benchmark_sharpe']:.3f}")
    
    if abs(performance['max_drawdown']) < abs(performance['benchmark_max_drawdown']):
        advantages.append(f"回撤控制更好: {performance['max_drawdown']:.2%} vs {performance['benchmark_max_drawdown']:.2%}")
    else:
        disadvantages.append(f"回撤控制较差: {performance['max_drawdown']:.2%} vs {performance['benchmark_max_drawdown']:.2%}")
    
    if performance['excess_return'] > 0:
        advantages.append(f"跑赢基准: +{performance['excess_return']:.2%}")
    else:
        disadvantages.append(f"跑输基准: {performance['excess_return']:.2%}")
    
    # 持仓均衡性
    btc_ratio = performance['position_stats']['BTCUSDT'] / total_days
    eth_ratio = performance['position_stats']['ETHUSDT'] / total_days
    if abs(btc_ratio - 0.5) < 0.1:  # 如果偏离50%不超过10%
        advantages.append(f"持仓分布均衡: BTC {btc_ratio:.1%}, ETH {eth_ratio:.1%}")
    
    for i, advantage in enumerate(advantages, 1):
        print(f"  {i}. ✅ {advantage}")
    
    if disadvantages:
        print(f"\n⚠️ 需要改进的方面:")
        for i, disadvantage in enumerate(disadvantages, 1):
            print(f"  {i}. ❌ {disadvantage}")
    
    # 策略评级
    print(f"\n🎯 策略综合评级:")
    
    score = 0
    max_score = 4
    
    # 夏普比率评分
    if performance['strategy_sharpe'] > performance['benchmark_sharpe']:
        score += 1
        print(f"  • 风险调整收益: ⭐ (夏普比率优于基准)")
    else:
        print(f"  • 风险调整收益: ❌ (夏普比率低于基准)")
    
    # 回撤控制评分
    if abs(performance['max_drawdown']) < abs(performance['benchmark_max_drawdown']):
        score += 1
        print(f"  • 回撤控制: ⭐ (最大回撤小于基准)")
    else:
        print(f"  • 回撤控制: ❌ (最大回撤大于基准)")
    
    # 收益率评分
    if performance['strategy_annual_return'] > 0.5:  # 年化收益率超过50%
        score += 1
        print(f"  • 收益能力: ⭐ (年化收益率 {performance['strategy_annual_return']:.1%})")
    else:
        print(f"  • 收益能力: ❌ (年化收益率 {performance['strategy_annual_return']:.1%})")
    
    # 稳定性评分
    if performance['win_rate'] > 0.4:  # 胜率超过40%
        score += 1
        print(f"  • 策略稳定性: ⭐ (胜率 {performance['win_rate']:.1%})")
    else:
        print(f"  • 策略稳定性: ❌ (胜率 {performance['win_rate']:.1%})")
    
    # 总评级
    rating_map = {
        4: "🌟🌟🌟🌟 优秀",
        3: "🌟🌟🌟 良好", 
        2: "🌟🌟 一般",
        1: "🌟 需要改进",
        0: "❌ 表现较差"
    }
    
    print(f"\n📈 综合评级: {rating_map[score]} ({score}/{max_score})")
    
    # 投资建议
    print(f"\n💡 投资建议:")
    if score >= 3:
        print(f"  • 🟢 推荐使用: 该策略在风险控制和收益能力方面表现良好")
        print(f"  • 适合风险偏好中等的投资者")
        print(f"  • 建议配置资金比例: 20-40%")
    elif score >= 2:
        print(f"  • 🟡 谨慎使用: 该策略表现中等，需要进一步优化")
        print(f"  • 适合风险偏好较低的投资者")
        print(f"  • 建议配置资金比例: 10-20%")
    else:
        print(f"  • 🔴 不建议使用: 该策略表现不佳，需要重新设计")
        print(f"  • 建议优化策略参数或更换策略逻辑")
    
    # 改进建议
    print(f"\n🔧 策略改进建议:")
    print(f"  1. 优化技术指标权重，提升选币准确性")
    print(f"  2. 增加市场环境判断，在不同市场状态下调整策略")
    print(f"  3. 考虑加入止损止盈机制，控制单笔交易风险")
    print(f"  4. 增加更多币种选择，扩大投资组合")
    print(f"  5. 优化交易频率，降低交易成本")
    
    # 数据文件位置
    print(f"\n📁 详细数据:")
    print(f"  • 策略配置和结果: {results_dir}")
    print(f"  • 资金曲线图: {os.path.join(results_dir, 'btc_eth_strategy_results.png')}")
    print(f"  • 详细交易记录: {os.path.join(results_dir, 'detailed_results.csv')}")
    print(f"  • 性能指标: {os.path.join(results_dir, 'performance_metrics.json')}")
    
    print(f"\n✅ 回测分析完成！")
    print("=" * 80)

if __name__ == "__main__":
    print_strategy_summary()
