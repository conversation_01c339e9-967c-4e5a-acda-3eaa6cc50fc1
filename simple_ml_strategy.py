# -*- coding: utf-8 -*-
"""
简化版机器学习策略：使用简单的线性回归预测BTC和ETH涨跌幅
然后选优交易，展示资金曲线图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
import os
import json

# 设置中文字体和忽略警告
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

class SimpleMLCryptoStrategy:
    """简化版机器学习加密货币预测策略"""
    
    def __init__(self):
        """初始化策略"""
        self.data_dir = "data/btc_eth_data"
        self.results_dir = "data/simple_ml_results"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 策略参数
        self.initial_capital = 100000  # 初始资金10万USDT
        self.transaction_cost = 0.001  # 交易费用0.1%
        self.lookback_days = 20  # 特征回看天数
        self.train_days = 500  # 训练数据天数
        
    def load_data(self):
        """加载BTC和ETH数据"""
        print("📊 加载比特币和以太坊历史数据...")
        
        # 加载合并数据
        combined_file = os.path.join(self.data_dir, "btc_eth_combined.csv")
        df = pd.read_csv(combined_file)
        df['date'] = pd.to_datetime(df['date'])
        
        print(f"✅ 数据加载完成:")
        print(f"  • 总记录数: {len(df):,} 条")
        print(f"  • 日期范围: {df['date'].min()} 到 {df['date'].max()}")
        print(f"  • BTC记录: {len(df[df['symbol']=='BTCUSDT']):,} 条")
        print(f"  • ETH记录: {len(df[df['symbol']=='ETHUSDT']):,} 条")
        
        return df
    
    def create_features(self, df):
        """创建简单的技术指标特征"""
        print("🔧 创建技术指标特征...")
        
        features_df = []
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_df = df[df['symbol'] == symbol].copy().sort_values('date').reset_index(drop=True)
            
            # 基础价格特征
            symbol_df['returns'] = symbol_df['close'].pct_change()
            symbol_df['log_returns'] = np.log(symbol_df['close'] / symbol_df['close'].shift(1))
            
            # 移动平均线
            for period in [5, 10, 20]:
                symbol_df[f'ma_{period}'] = symbol_df['close'].rolling(period).mean()
                symbol_df[f'ma_ratio_{period}'] = symbol_df['close'] / symbol_df[f'ma_{period}']
            
            # 简单RSI
            delta = symbol_df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            symbol_df['rsi'] = 100 - (100 / (1 + rs))
            
            # 价格位置（20日内的相对位置）
            symbol_df['price_position'] = (symbol_df['close'] - symbol_df['close'].rolling(20).min()) / \
                                        (symbol_df['close'].rolling(20).max() - symbol_df['close'].rolling(20).min())
            
            # 成交量比率
            symbol_df['volume_ratio'] = symbol_df['volume'] / symbol_df['volume'].rolling(20).mean()
            
            # 波动率
            symbol_df['volatility'] = symbol_df['returns'].rolling(20).std()
            
            # 滞后特征
            for lag in [1, 2, 3, 5]:
                symbol_df[f'returns_lag_{lag}'] = symbol_df['returns'].shift(lag)
            
            # 目标变量：未来1天收益率
            symbol_df['target'] = symbol_df['returns'].shift(-1)
            
            features_df.append(symbol_df)
        
        # 合并数据
        combined_features = pd.concat(features_df, ignore_index=True)
        combined_features = combined_features.sort_values(['symbol', 'date']).reset_index(drop=True)
        
        print(f"✅ 特征创建完成，共 {combined_features.shape[1]} 个特征")
        
        return combined_features
    
    def simple_linear_regression(self, X, y):
        """简单的线性回归实现"""
        # 添加截距项
        X_with_intercept = np.column_stack([np.ones(X.shape[0]), X])
        
        # 计算回归系数: β = (X'X)^(-1)X'y
        try:
            XtX = np.dot(X_with_intercept.T, X_with_intercept)
            XtX_inv = np.linalg.inv(XtX)
            Xty = np.dot(X_with_intercept.T, y)
            beta = np.dot(XtX_inv, Xty)
            return beta
        except:
            # 如果矩阵不可逆，返回零系数
            return np.zeros(X_with_intercept.shape[1])
    
    def predict_linear(self, X, beta):
        """线性回归预测"""
        X_with_intercept = np.column_stack([np.ones(X.shape[0]), X])
        return np.dot(X_with_intercept, beta)
    
    def rolling_prediction(self, df):
        """滚动预测"""
        print("🤖 执行滚动预测...")
        
        # 选择特征列
        feature_cols = ['ma_ratio_5', 'ma_ratio_10', 'ma_ratio_20', 'rsi', 'price_position', 
                       'volume_ratio', 'volatility', 'returns_lag_1', 'returns_lag_2', 'returns_lag_3']
        
        predictions = {}
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            print(f"📈 预测 {symbol}...")
            
            symbol_df = df[df['symbol'] == symbol].copy().sort_values('date').reset_index(drop=True)
            
            # 删除包含NaN的行
            symbol_df = symbol_df.dropna()
            
            predictions_list = []
            
            # 滚动预测
            for i in range(self.train_days, len(symbol_df) - 1):
                # 训练数据
                train_start = max(0, i - self.train_days)
                train_end = i
                
                train_data = symbol_df.iloc[train_start:train_end]
                X_train = train_data[feature_cols].values
                y_train = train_data['target'].values
                
                # 移除NaN
                valid_idx = ~(np.isnan(X_train).any(axis=1) | np.isnan(y_train))
                X_train = X_train[valid_idx]
                y_train = y_train[valid_idx]
                
                if len(X_train) < 10:  # 需要足够的训练数据
                    continue
                
                # 训练模型
                beta = self.simple_linear_regression(X_train, y_train)
                
                # 预测
                X_test = symbol_df.iloc[i][feature_cols].values.reshape(1, -1)
                if not np.isnan(X_test).any():
                    pred = self.predict_linear(X_test, beta)[0]
                    
                    predictions_list.append({
                        'date': symbol_df.iloc[i]['date'],
                        'close': symbol_df.iloc[i]['close'],
                        'prediction': pred,
                        'actual': symbol_df.iloc[i]['target']
                    })
            
            predictions[symbol] = pd.DataFrame(predictions_list)
            print(f"  • {symbol} 预测完成: {len(predictions_list)} 个预测点")
        
        return predictions
    
    def backtest_strategy(self, predictions):
        """回测策略"""
        print("📊 执行策略回测...")
        
        # 获取共同的日期
        btc_dates = set(predictions['BTCUSDT']['date'])
        eth_dates = set(predictions['ETHUSDT']['date'])
        common_dates = sorted(btc_dates & eth_dates)
        
        print(f"📅 共同交易日期: {len(common_dates)} 天")
        
        strategy_results = []
        capital = self.initial_capital
        position = None
        
        for date in common_dates:
            # 获取当日预测
            btc_pred = predictions['BTCUSDT'][predictions['BTCUSDT']['date'] == date]
            eth_pred = predictions['ETHUSDT'][predictions['ETHUSDT']['date'] == date]
            
            if len(btc_pred) == 0 or len(eth_pred) == 0:
                continue
            
            btc_prediction = btc_pred.iloc[0]['prediction']
            eth_prediction = eth_pred.iloc[0]['prediction']
            btc_actual = btc_pred.iloc[0]['actual']
            eth_actual = eth_pred.iloc[0]['actual']
            
            # 策略逻辑：选择预测收益率最高的币种
            if btc_prediction > eth_prediction and btc_prediction > 0.001:  # 阈值：0.1%
                new_position = 'BTCUSDT'
                predicted_return = btc_prediction
                actual_return = btc_actual
            elif eth_prediction > btc_prediction and eth_prediction > 0.001:
                new_position = 'ETHUSDT'
                predicted_return = eth_prediction
                actual_return = eth_actual
            else:
                new_position = None  # 空仓
                predicted_return = 0
                actual_return = 0
            
            # 计算收益
            if position is not None and position == new_position:
                # 继续持有，获得实际收益
                if not pd.isna(actual_return):
                    net_return = actual_return - self.transaction_cost * 0.5  # 持有成本较低
                    capital *= (1 + net_return)
            elif new_position is not None:
                # 新开仓或换仓
                if not pd.isna(actual_return):
                    net_return = actual_return - self.transaction_cost
                    capital *= (1 + net_return)
                    if position is not None and position != new_position:
                        # 换仓额外成本
                        capital *= (1 - self.transaction_cost * 0.5)
            
            # 更新持仓
            position = new_position
            
            # 计算基准收益（等权重）
            benchmark_return = (btc_actual + eth_actual) / 2 if not (pd.isna(btc_actual) or pd.isna(eth_actual)) else 0
            
            strategy_results.append({
                'date': date,
                'capital': capital,
                'position': position,
                'btc_prediction': btc_prediction,
                'eth_prediction': eth_prediction,
                'btc_actual': btc_actual,
                'eth_actual': eth_actual,
                'predicted_return': predicted_return,
                'actual_return': actual_return,
                'benchmark_return': benchmark_return
            })
        
        strategy_df = pd.DataFrame(strategy_results)
        
        # 计算基准资金曲线
        strategy_df['benchmark_capital'] = self.initial_capital
        for i in range(1, len(strategy_df)):
            prev_capital = strategy_df.iloc[i-1]['benchmark_capital']
            benchmark_return = strategy_df.iloc[i]['benchmark_return']
            if not pd.isna(benchmark_return):
                strategy_df.iloc[i, strategy_df.columns.get_loc('benchmark_capital')] = prev_capital * (1 + benchmark_return - self.transaction_cost)
            else:
                strategy_df.iloc[i, strategy_df.columns.get_loc('benchmark_capital')] = prev_capital
        
        return strategy_df
    
    def analyze_performance(self, results_df):
        """分析策略表现"""
        print("📈 分析策略表现...")
        
        # 计算收益率
        results_df['strategy_returns'] = results_df['capital'].pct_change()
        results_df['benchmark_returns'] = results_df['benchmark_capital'].pct_change()
        
        # 计算累计收益
        strategy_total_return = (results_df['capital'].iloc[-1] / self.initial_capital) - 1
        benchmark_total_return = (results_df['benchmark_capital'].iloc[-1] / self.initial_capital) - 1
        
        # 计算年化收益率
        days = len(results_df)
        strategy_annual_return = (1 + strategy_total_return) ** (365 / days) - 1
        benchmark_annual_return = (1 + benchmark_total_return) ** (365 / days) - 1
        
        # 计算最大回撤
        results_df['strategy_peak'] = results_df['capital'].cummax()
        results_df['strategy_drawdown'] = (results_df['capital'] - results_df['strategy_peak']) / results_df['strategy_peak']
        max_drawdown = results_df['strategy_drawdown'].min()
        
        results_df['benchmark_peak'] = results_df['benchmark_capital'].cummax()
        results_df['benchmark_drawdown'] = (results_df['benchmark_capital'] - results_df['benchmark_peak']) / results_df['benchmark_peak']
        benchmark_max_drawdown = results_df['benchmark_drawdown'].min()
        
        # 计算夏普比率
        strategy_sharpe = results_df['strategy_returns'].mean() / results_df['strategy_returns'].std() * np.sqrt(252) if results_df['strategy_returns'].std() > 0 else 0
        benchmark_sharpe = results_df['benchmark_returns'].mean() / results_df['benchmark_returns'].std() * np.sqrt(252) if results_df['benchmark_returns'].std() > 0 else 0
        
        # 计算胜率
        win_rate = (results_df['strategy_returns'] > 0).mean()
        
        # 持仓统计
        position_stats = results_df['position'].value_counts()
        
        performance = {
            'strategy_total_return': strategy_total_return,
            'benchmark_total_return': benchmark_total_return,
            'strategy_annual_return': strategy_annual_return,
            'benchmark_annual_return': benchmark_annual_return,
            'excess_return': strategy_total_return - benchmark_total_return,
            'max_drawdown': max_drawdown,
            'benchmark_max_drawdown': benchmark_max_drawdown,
            'strategy_sharpe': strategy_sharpe,
            'benchmark_sharpe': benchmark_sharpe,
            'win_rate': win_rate,
            'total_trades': len(results_df),
            'final_capital': results_df['capital'].iloc[-1],
            'benchmark_final_capital': results_df['benchmark_capital'].iloc[-1],
            'position_stats': position_stats.to_dict()
        }
        
        return performance, results_df
    
    def plot_results(self, results_df, performance):
        """绘制结果图表"""
        print("📊 生成结果图表...")
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('简化版机器学习加密货币预测策略回测结果', fontsize=16, fontweight='bold')
        
        # 1. 资金曲线图
        ax1 = axes[0, 0]
        ax1.plot(results_df['date'], results_df['capital'], label='ML策略', linewidth=2, color='blue')
        ax1.plot(results_df['date'], results_df['benchmark_capital'], label='等权重基准', linewidth=2, color='red', alpha=0.7)
        ax1.set_title('资金曲线对比', fontsize=14, fontweight='bold')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('资金 (USDT)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 格式化日期
        if len(results_df) > 100:
            ax1.xaxis.set_major_locator(plt.MaxNLocator(10))
        
        # 添加收益率标注
        final_strategy_return = performance['strategy_total_return']
        final_benchmark_return = performance['benchmark_total_return']
        ax1.text(0.02, 0.98, f'策略收益: {final_strategy_return:.2%}', transform=ax1.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        ax1.text(0.02, 0.90, f'基准收益: {final_benchmark_return:.2%}', transform=ax1.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
        
        # 2. 回撤图
        ax2 = axes[0, 1]
        ax2.fill_between(results_df['date'], results_df['strategy_drawdown'], 0, 
                        alpha=0.3, color='red', label='策略回撤')
        ax2.fill_between(results_df['date'], results_df['benchmark_drawdown'], 0, 
                        alpha=0.3, color='orange', label='基准回撤')
        ax2.set_title('回撤分析', fontsize=14, fontweight='bold')
        ax2.set_xlabel('日期')
        ax2.set_ylabel('回撤比例')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        if len(results_df) > 100:
            ax2.xaxis.set_major_locator(plt.MaxNLocator(10))
        
        # 3. 持仓分布
        ax3 = axes[1, 0]
        position_counts = results_df['position'].value_counts()
        position_counts = position_counts[position_counts.index.notna()]  # 移除空仓
        if len(position_counts) > 0:
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
            wedges, texts, autotexts = ax3.pie(position_counts.values, labels=position_counts.index, 
                                              autopct='%1.1f%%', colors=colors[:len(position_counts)])
            ax3.set_title('持仓分布', fontsize=14, fontweight='bold')
        
        # 4. 性能指标表
        ax4 = axes[1, 1]
        ax4.axis('off')
        
        metrics_text = f"""
策略性能指标

总收益率:     {performance['strategy_total_return']:.2%}
基准收益率:   {performance['benchmark_total_return']:.2%}
超额收益:     {performance['excess_return']:.2%}
年化收益率:   {performance['strategy_annual_return']:.2%}
最大回撤:     {performance['max_drawdown']:.2%}
夏普比率:     {performance['strategy_sharpe']:.3f}
胜率:         {performance['win_rate']:.2%}
交易天数:     {performance['total_trades']}
最终资金:     {performance['final_capital']:,.0f} USDT
        """
        
        ax4.text(0.1, 0.9, metrics_text, transform=ax4.transAxes, fontsize=12,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = os.path.join(self.results_dir, 'simple_ml_strategy_results.png')
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {chart_file}")
        
        plt.show()
        
        return chart_file
    
    def save_results(self, results_df, performance):
        """保存结果"""
        print("💾 保存回测结果...")
        
        # 保存详细结果
        results_file = os.path.join(self.results_dir, 'detailed_results.csv')
        results_df.to_csv(results_file, index=False, encoding='utf-8-sig')
        
        # 保存性能指标
        performance_file = os.path.join(self.results_dir, 'performance_metrics.json')
        with open(performance_file, 'w', encoding='utf-8') as f:
            json.dump(performance, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✅ 结果已保存:")
        print(f"  • 详细结果: {results_file}")
        print(f"  • 性能指标: {performance_file}")
    
    def run(self):
        """执行完整的策略回测流程"""
        print("🚀 开始简化版机器学习加密货币预测策略回测...")
        print("=" * 80)
        
        try:
            # 1. 加载数据
            df = self.load_data()
            
            # 2. 创建特征
            features_df = self.create_features(df)
            
            # 3. 滚动预测
            predictions = self.rolling_prediction(features_df)
            
            # 4. 回测策略
            results_df = self.backtest_strategy(predictions)
            
            # 5. 分析表现
            performance, results_df = self.analyze_performance(results_df)
            
            # 6. 绘制图表
            chart_file = self.plot_results(results_df, performance)
            
            # 7. 保存结果
            self.save_results(results_df, performance)
            
            # 8. 显示总结
            print("\n🎉 简化版ML策略回测完成！")
            print("=" * 80)
            print(f"📈 策略表现总结:")
            print(f"  • 策略总收益率: {performance['strategy_total_return']:.2%}")
            print(f"  • 基准总收益率: {performance['benchmark_total_return']:.2%}")
            print(f"  • 超额收益: {performance['excess_return']:.2%}")
            print(f"  • 年化收益率: {performance['strategy_annual_return']:.2%}")
            print(f"  • 最大回撤: {performance['max_drawdown']:.2%}")
            print(f"  • 夏普比率: {performance['strategy_sharpe']:.3f}")
            print(f"  • 胜率: {performance['win_rate']:.2%}")
            print(f"  • 最终资金: {performance['final_capital']:,.0f} USDT")
            print(f"📁 结果保存在: {self.results_dir}")
            
            return True
            
        except Exception as e:
            print(f"❌ 策略回测失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    strategy = SimpleMLCryptoStrategy()
    success = strategy.run()
    
    if success:
        print("\n✅ 程序执行成功")
    else:
        print("\n❌ 程序执行失败")

if __name__ == "__main__":
    main()
