# -*- coding: utf-8 -*-
"""
获取币安官方507个USDT永续合约的全量历史数据
使用成功的连接方法，确保获取官方数据
支持断点续传、进度保存、错误恢复
"""

import requests
import json
import csv
import time
import os
import pandas as pd
from datetime import datetime, timedelta
import ssl
import urllib3
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class AllPerpetualHistoricalDataFetcher:
    """所有永续合约历史数据获取器"""
    
    def __init__(self):
        """初始化获取器"""
        self.session = self.create_optimized_session()
        self.data_dir = "data"
        self.historical_dir = os.path.join(self.data_dir, "perpetual_historical")
        self.progress_file = os.path.join(self.data_dir, "perpetual_fetch_progress.json")
        self.ensure_directories()
        self.lock = threading.Lock()
        
        # 配置参数
        self.max_workers = 3  # 并发数量，避免API限制
        self.requests_per_minute = 1200  # API限制
        self.delay_between_requests = 0.1  # 请求间隔
        
    def ensure_directories(self):
        """确保目录存在"""
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.historical_dir, exist_ok=True)
    
    def create_optimized_session(self):
        """创建优化的HTTP会话 - 使用成功的连接方法"""
        session = requests.Session()
        
        # 关键请求头设置 - 基于成功经验
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache'
        })
        
        # 重试策略 - 基于成功经验
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=2,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def load_contracts_with_dates(self):
        """加载合约列表及其开始日期"""
        start_dates_file = os.path.join(self.data_dir, 'all_perpetual_contracts_start_dates.json')
        
        if not os.path.exists(start_dates_file):
            print("❌ 永续合约开始日期文件不存在，请先运行 get_perpetual_start_dates.py")
            return []
        
        try:
            with open(start_dates_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            results = data.get('results', [])
            # 只处理成功获取开始日期的合约
            active_contracts = [r for r in results if r['status'] == 'SUCCESS']
            
            print(f"✅ 成功加载 {len(active_contracts)} 个活跃永续合约")
            return active_contracts
            
        except Exception as e:
            print(f"❌ 加载永续合约开始日期文件失败: {str(e)}")
            return []
    
    def load_progress(self):
        """加载进度"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def save_progress(self, progress):
        """保存进度"""
        with self.lock:
            try:
                with open(self.progress_file, 'w', encoding='utf-8') as f:
                    json.dump(progress, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"⚠️ 保存进度失败: {e}")
    
    def get_contract_historical_data(self, contract_info, progress):
        """获取单个合约的历史数据"""
        symbol = contract_info['symbol']
        start_date = contract_info.get('start_date')
        
        if not start_date:
            return {
                'symbol': symbol,
                'status': 'NO_START_DATE',
                'error': '无开始日期',
                'records': 0
            }
        
        # 检查是否已完成
        if progress.get(symbol, {}).get('status') == 'COMPLETED':
            existing_file = os.path.join(self.historical_dir, f"{symbol}_historical.csv")
            if os.path.exists(existing_file):
                try:
                    df = pd.read_csv(existing_file)
                    return {
                        'symbol': symbol,
                        'status': 'ALREADY_COMPLETED',
                        'records': len(df),
                        'file': existing_file
                    }
                except:
                    pass
        
        try:
            # 计算时间范围
            start_timestamp = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
            end_timestamp = int(datetime.now().timestamp() * 1000)
            
            all_data = []
            current_start = start_timestamp
            
            print(f"📡 获取 {symbol} 历史数据 (从 {start_date} 开始)...")
            
            while current_start < end_timestamp:
                # 使用币安期货K线API
                url = "https://fapi.binance.com/fapi/v1/klines"
                params = {
                    'symbol': symbol,
                    'interval': '1d',
                    'startTime': current_start,
                    'limit': 1000  # 每次获取1000条
                }
                
                response = self.session.get(url, params=params, timeout=60, verify=True)
                
                if response.status_code == 200:
                    data = response.json()
                    if not data:
                        break
                    
                    # 处理数据
                    for item in data:
                        kline_data = {
                            'timestamp': item[0],
                            'date': datetime.fromtimestamp(item[0] / 1000).strftime('%Y-%m-%d'),
                            'open': float(item[1]),
                            'high': float(item[2]),
                            'low': float(item[3]),
                            'close': float(item[4]),
                            'volume': float(item[5]),
                            'close_time': item[6],
                            'quote_volume': float(item[7]),
                            'trades': int(item[8]),
                            'taker_buy_volume': float(item[9]),
                            'taker_buy_quote_volume': float(item[10])
                        }
                        all_data.append(kline_data)
                    
                    # 更新下次开始时间
                    current_start = data[-1][6] + 1  # 使用最后一条的close_time + 1
                    
                    # 速率控制
                    time.sleep(self.delay_between_requests)
                    
                elif response.status_code == 400:
                    # 合约可能已下线
                    return {
                        'symbol': symbol,
                        'status': 'CONTRACT_NOT_FOUND',
                        'error': '合约不存在或已下线',
                        'records': 0
                    }
                else:
                    return {
                        'symbol': symbol,
                        'status': 'API_ERROR',
                        'error': f'HTTP {response.status_code}',
                        'records': 0
                    }
            
            if all_data:
                # 保存数据
                df = pd.DataFrame(all_data)
                df = df.drop_duplicates(subset=['timestamp'])  # 去重
                df = df.sort_values('timestamp')  # 排序
                
                # 保存为CSV
                csv_file = os.path.join(self.historical_dir, f"{symbol}_historical.csv")
                df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                
                # 保存为Parquet（压缩格式）
                parquet_file = os.path.join(self.historical_dir, f"{symbol}_historical.parquet")
                df.to_parquet(parquet_file, index=False)
                
                # 更新进度
                progress[symbol] = {
                    'status': 'COMPLETED',
                    'records': len(df),
                    'start_date': start_date,
                    'end_date': df['date'].iloc[-1],
                    'csv_file': csv_file,
                    'parquet_file': parquet_file,
                    'completed_time': datetime.now().isoformat()
                }
                
                return {
                    'symbol': symbol,
                    'status': 'SUCCESS',
                    'records': len(df),
                    'start_date': start_date,
                    'end_date': df['date'].iloc[-1],
                    'csv_file': csv_file,
                    'parquet_file': parquet_file
                }
            else:
                return {
                    'symbol': symbol,
                    'status': 'NO_DATA',
                    'error': '无历史数据',
                    'records': 0
                }
                
        except requests.exceptions.Timeout:
            return {
                'symbol': symbol,
                'status': 'TIMEOUT',
                'error': '请求超时',
                'records': 0
            }
        except Exception as e:
            return {
                'symbol': symbol,
                'status': 'ERROR',
                'error': str(e),
                'records': 0
            }
    
    def fetch_all_historical_data(self, contracts):
        """批量获取所有合约的历史数据"""
        print(f"🚀 开始获取 {len(contracts)} 个永续合约的全量历史数据...")
        print("=" * 80)
        
        # 加载进度
        progress = self.load_progress()
        
        # 统计信息
        total_contracts = len(contracts)
        completed_contracts = len([c for c in contracts if progress.get(c['symbol'], {}).get('status') == 'COMPLETED'])
        remaining_contracts = total_contracts - completed_contracts
        
        print(f"📊 任务状态:")
        print(f"  • 总合约数: {total_contracts}")
        print(f"  • 已完成: {completed_contracts}")
        print(f"  • 剩余: {remaining_contracts}")
        
        if remaining_contracts == 0:
            print("✅ 所有合约数据已获取完成！")
            return self.generate_summary(contracts, progress)
        
        results = []
        start_time = datetime.now()
        
        # 使用线程池并发获取
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_contract = {}
            for contract in contracts:
                symbol = contract['symbol']
                if progress.get(symbol, {}).get('status') != 'COMPLETED':
                    future = executor.submit(self.get_contract_historical_data, contract, progress)
                    future_to_contract[future] = contract
            
            # 处理结果
            completed_count = completed_contracts
            for future in as_completed(future_to_contract):
                contract = future_to_contract[future]
                symbol = contract['symbol']
                
                try:
                    result = future.result()
                    results.append(result)
                    
                    # 显示结果
                    if result['status'] == 'SUCCESS':
                        records = result['records']
                        start_date = result['start_date']
                        end_date = result['end_date']
                        print(f"✅ {symbol:20s}: {records:6d} 条记录 ({start_date} 到 {end_date})")
                        
                        # 更新进度
                        progress[symbol] = {
                            'status': 'COMPLETED',
                            'records': records,
                            'start_date': start_date,
                            'end_date': end_date,
                            'csv_file': result['csv_file'],
                            'parquet_file': result['parquet_file'],
                            'completed_time': datetime.now().isoformat()
                        }
                        
                    elif result['status'] == 'ALREADY_COMPLETED':
                        records = result['records']
                        print(f"✅ {symbol:20s}: {records:6d} 条记录 (已完成)")
                        
                    elif result['status'] == 'CONTRACT_NOT_FOUND':
                        print(f"⚠️  {symbol:20s}: 合约已下线")
                        progress[symbol] = {
                            'status': 'DELISTED',
                            'error': result['error'],
                            'completed_time': datetime.now().isoformat()
                        }
                        
                    else:
                        print(f"❌ {symbol:20s}: {result['status']} - {result.get('error', '')}")
                        progress[symbol] = {
                            'status': 'FAILED',
                            'error': result.get('error', ''),
                            'last_attempt': datetime.now().isoformat()
                        }
                    
                    completed_count += 1
                    
                    # 每10个保存一次进度
                    if completed_count % 10 == 0:
                        self.save_progress(progress)
                        elapsed = (datetime.now() - start_time).total_seconds()
                        avg_time = elapsed / (completed_count - completed_contracts)
                        remaining = total_contracts - completed_count
                        eta = remaining * avg_time
                        
                        print(f"📊 进度: {completed_count}/{total_contracts} ({completed_count/total_contracts*100:.1f}%), 预计剩余: {eta/60:.1f}分钟")
                
                except Exception as e:
                    print(f"❌ {symbol:20s}: 处理异常 - {str(e)}")
                    results.append({
                        'symbol': symbol,
                        'status': 'EXCEPTION',
                        'error': str(e),
                        'records': 0
                    })
        
        # 最终保存进度
        self.save_progress(progress)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print("=" * 80)
        print(f"⏱️  总耗时: {duration/60:.1f} 分钟")
        print(f"📊 处理速度: {len(results)/duration:.2f} 个/秒")
        
        return self.generate_summary(contracts, progress)
    
    def generate_summary(self, contracts, progress):
        """生成汇总报告"""
        print(f"\n📊 历史数据获取汇总:")
        
        # 统计各种状态
        completed = len([p for p in progress.values() if p.get('status') == 'COMPLETED'])
        delisted = len([p for p in progress.values() if p.get('status') == 'DELISTED'])
        failed = len([p for p in progress.values() if p.get('status') == 'FAILED'])
        total = len(contracts)
        
        print(f"  • 总合约数: {total}")
        print(f"  • 成功获取: {completed} ({completed/total*100:.1f}%)")
        print(f"  • 已下线: {delisted} ({delisted/total*100:.1f}%)")
        print(f"  • 失败: {failed} ({failed/total*100:.1f}%)")
        
        # 统计数据量
        total_records = sum([p.get('records', 0) for p in progress.values() if p.get('records')])
        print(f"  • 总记录数: {total_records:,} 条")
        
        # 计算文件大小
        total_size = 0
        for symbol, info in progress.items():
            if info.get('status') == 'COMPLETED' and info.get('csv_file'):
                try:
                    size = os.path.getsize(info['csv_file'])
                    total_size += size
                except:
                    pass
        
        print(f"  • 总文件大小: {total_size/(1024*1024):.1f} MB")
        
        # 保存汇总报告
        summary = {
            'fetch_time': datetime.now().isoformat(),
            'total_contracts': total,
            'completed_contracts': completed,
            'delisted_contracts': delisted,
            'failed_contracts': failed,
            'total_records': total_records,
            'total_size_mb': total_size/(1024*1024),
            'data_directory': self.historical_dir,
            'progress': progress
        }
        
        summary_file = os.path.join(self.data_dir, 'perpetual_historical_summary.json')
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"📁 汇总报告已保存: {summary_file}")
        
        return summary
    
    def run(self):
        """执行获取流程"""
        print("🚀 开始获取507个永续合约的全量历史数据...")
        print("=" * 80)
        
        # 1. 加载合约列表
        contracts = self.load_contracts_with_dates()
        if not contracts:
            print("❌ 无法加载合约列表，程序退出")
            return False
        
        # 2. 获取历史数据
        summary = self.fetch_all_historical_data(contracts)
        
        print(f"\n🎉 历史数据获取完成！")
        print(f"📊 成功获取 {summary['completed_contracts']} 个合约的历史数据")
        print(f"📁 数据保存在 {self.historical_dir} 目录下")
        print(f"✅ 数据来源: 100% 币安官方期货API")
        
        return True

def main():
    """主函数"""
    try:
        fetcher = AllPerpetualHistoricalDataFetcher()
        success = fetcher.run()
        
        if success:
            print("\n✅ 程序执行成功")
        else:
            print("\n❌ 程序执行失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
    
    print("\n程序结束")

if __name__ == "__main__":
    main()
