#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
507合约历史数据获取测试工具

功能：
1. 测试币安API连接
2. 获取少量合约的历史数据作为示例
3. 验证数据获取流程

作者：加密货币量化交易系统
日期：2025年1月28日
"""

import requests
import pandas as pd
import json
import os
import time
from datetime import datetime

def test_api_connection():
    """测试API连接"""
    print("🔗 测试币安期货API连接...")
    
    try:
        url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            symbols_count = len(data.get('symbols', []))
            print(f"✅ API连接成功，获取到 {symbols_count} 个合约信息")
            return True
        else:
            print(f"❌ API连接失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API连接异常: {str(e)}")
        return False

def load_sample_contracts():
    """加载示例合约列表"""
    print("\n📋 加载示例合约列表...")
    
    try:
        contracts_file = "data/all_perpetual_contracts_last_dates.json"
        
        if not os.path.exists(contracts_file):
            print(f"❌ 未找到合约列表文件: {contracts_file}")
            return []
        
        with open(contracts_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 取前5个成功的合约作为示例
        sample_symbols = []
        for contract in data['results']:
            if contract['status'] == 'SUCCESS' and len(sample_symbols) < 5:
                sample_symbols.append(contract['symbol'])
        
        print(f"✅ 加载示例合约: {sample_symbols}")
        return sample_symbols
        
    except Exception as e:
        print(f"❌ 加载合约列表失败: {str(e)}")
        return []

def get_contract_history(symbol, max_batches=3):
    """获取单个合约的历史数据（限制批次）"""
    print(f"\n📊 获取 {symbol} 历史数据...")
    
    try:
        base_url = "https://fapi.binance.com/fapi/v1"
        all_data = []
        end_time = None
        batch_count = 0
        
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        while batch_count < max_batches:
            url = f"{base_url}/klines"
            params = {
                'symbol': symbol,
                'interval': '1d',
                'limit': 1000  # 减少单次请求量
            }
            
            if end_time:
                params['endTime'] = end_time
            
            print(f"  📈 {symbol}: 获取第 {batch_count + 1} 批数据...")
            
            response = session.get(url, params=params, timeout=30)
            
            if response.status_code != 200:
                print(f"❌ {symbol} API请求失败: {response.status_code}")
                break
            
            klines = response.json()
            
            if not klines:
                print(f"  📭 {symbol}: 无更多数据")
                break
            
            all_data.extend(klines)
            batch_count += 1
            
            print(f"  📊 {symbol}: 第{batch_count}批，获取 {len(klines)} 条，累计 {len(all_data)} 条")
            
            # 检查是否获取完所有数据
            if len(klines) < 1000:
                print(f"  ✅ {symbol}: 数据获取完成")
                break
            
            # 设置下一批的结束时间
            end_time = klines[0][0] - 1
            
            # 速率控制
            time.sleep(1.5)
        
        if not all_data:
            print(f"❌ {symbol}: 未获取到数据")
            return None
        
        # 转换为DataFrame
        df = pd.DataFrame(all_data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_volume', 'count', 'taker_buy_volume',
            'taker_buy_quote_volume', 'ignore'
        ])
        
        # 数据处理
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df['date'] = df['timestamp'].dt.date
        
        # 数据类型转换
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 排序和去重
        df = df.sort_values('timestamp').drop_duplicates(subset=['timestamp']).reset_index(drop=True)
        
        print(f"✅ {symbol}: 成功获取 {len(df)} 条历史数据")
        print(f"  📅 时间范围: {df['date'].min()} 至 {df['date'].max()}")
        
        return df
        
    except Exception as e:
        print(f"❌ {symbol} 获取历史数据失败: {str(e)}")
        return None

def save_sample_data(df, symbol):
    """保存示例数据"""
    try:
        # 创建目录
        data_dir = "sample_507_data"
        os.makedirs(data_dir, exist_ok=True)
        
        # 保存CSV文件
        csv_file = os.path.join(data_dir, f"{symbol}_sample.csv")
        df_save = df[['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume']].copy()
        df_save.to_csv(csv_file, index=False, encoding='utf-8-sig')
        
        # 生成统计信息
        stats = {
            'symbol': symbol,
            'download_time': datetime.now().isoformat(),
            'data_points': len(df),
            'date_range': {
                'start': str(df['date'].min()),
                'end': str(df['date'].max())
            },
            'price_range': {
                'min': float(df['low'].min()),
                'max': float(df['high'].max()),
                'latest': float(df['close'].iloc[-1])
            },
            'volume_stats': {
                'total': float(df['volume'].sum()),
                'average': float(df['volume'].mean()),
                'max': float(df['volume'].max())
            },
            'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
            'file_path': csv_file
        }
        
        # 保存统计信息
        stats_file = os.path.join(data_dir, f"{symbol}_stats.json")
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"💾 {symbol}: 数据已保存到 {csv_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ {symbol} 保存数据失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 507合约历史数据获取测试工具")
    print("=" * 50)
    
    # 1. 测试API连接
    if not test_api_connection():
        print("❌ API连接失败，程序退出")
        return
    
    # 2. 加载示例合约
    sample_symbols = load_sample_contracts()
    if not sample_symbols:
        print("❌ 无法获取示例合约，程序退出")
        return
    
    print(f"\n🎯 将测试获取 {len(sample_symbols)} 个合约的历史数据")
    
    # 3. 获取示例数据
    success_count = 0
    failed_count = 0
    
    for i, symbol in enumerate(sample_symbols, 1):
        print(f"\n[{i}/{len(sample_symbols)}] 处理 {symbol}...")
        
        # 获取历史数据
        df = get_contract_history(symbol, max_batches=3)  # 限制为3批，约3000条数据
        
        if df is not None:
            # 保存数据
            if save_sample_data(df, symbol):
                success_count += 1
            else:
                failed_count += 1
        else:
            failed_count += 1
        
        # 显示进度
        print(f"📈 进度: {i}/{len(sample_symbols)} ({i/len(sample_symbols)*100:.1f}%)")
    
    # 4. 生成总结报告
    print(f"\n📊 测试完成总结:")
    print(f"✅ 成功: {success_count} 个合约")
    print(f"❌ 失败: {failed_count} 个合约")
    print(f"📈 成功率: {success_count/(success_count+failed_count)*100:.1f}%")
    
    if success_count > 0:
        print(f"\n💡 基于测试结果，507个合约的完整历史数据获取是可行的")
        print(f"📁 示例数据保存在: sample_507_data/ 目录")
        print(f"⏱️ 预估全量下载时间: {len(sample_symbols) * 507 / len(sample_symbols) * 2 / 60:.0f} 分钟")
    
    print(f"\n✅ 测试完成！")

if __name__ == "__main__":
    main()
    print("\n按回车键退出...")
    input()
