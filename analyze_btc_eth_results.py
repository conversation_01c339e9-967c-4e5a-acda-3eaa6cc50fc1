# -*- coding: utf-8 -*-
"""
分析BTC-ETH选优策略回测结果
"""

import pandas as pd
import json
import os

def analyze_btc_eth_results():
    """分析BTC-ETH策略回测结果"""
    
    results_dir = "data/btc_eth_strategy_results"
    
    # 读取性能指标
    with open(os.path.join(results_dir, "performance_metrics.json"), 'r', encoding='utf-8') as f:
        performance = json.load(f)
    
    # 读取详细结果
    detailed_results = pd.read_csv(os.path.join(results_dir, "detailed_results.csv"))
    
    print("🎉 BTC-ETH简单选优策略回测结果分析")
    print("=" * 80)
    
    # 基本信息
    print(f"📊 基本信息:")
    print(f"  • 回测期间: {performance['years']:.1f} 年")
    print(f"  • 交易天数: {performance['total_trades']} 天")
    print(f"  • 初始资金: 100,000 USDT")
    print(f"  • 交易费用: 0.1%")
    
    # 收益率分析
    print(f"\n💰 收益率分析:")
    print(f"  • 策略总收益率: {performance['strategy_total_return']:.2%}")
    print(f"  • 基准总收益率: {performance['benchmark_total_return']:.2%}")
    print(f"  • 超额收益: {performance['excess_return']:.2%}")
    print(f"  • 策略年化收益率: {performance['strategy_annual_return']:.2%}")
    print(f"  • 基准年化收益率: {performance['benchmark_annual_return']:.2%}")
    
    # 风险指标
    print(f"\n⚠️ 风险指标:")
    print(f"  • 策略最大回撤: {performance['max_drawdown']:.2%}")
    print(f"  • 基准最大回撤: {performance['benchmark_max_drawdown']:.2%}")
    print(f"  • 策略夏普比率: {performance['strategy_sharpe']:.3f}")
    print(f"  • 基准夏普比率: {performance['benchmark_sharpe']:.3f}")
    print(f"  • 胜率: {performance['win_rate']:.2%}")
    
    # 最终资金
    print(f"\n💵 最终资金:")
    print(f"  • 策略最终资金: {performance['final_capital']:,.0f} USDT")
    print(f"  • 基准最终资金: {performance['benchmark_final_capital']:,.0f} USDT")
    print(f"  • 资金差额: {performance['final_capital'] - performance['benchmark_final_capital']:,.0f} USDT")
    
    # 持仓分布
    print(f"\n📊 持仓分布:")
    total_days = sum(performance['position_stats'].values())
    for position, days in performance['position_stats'].items():
        percentage = days / total_days * 100
        print(f"  • {position}: {days} 天 ({percentage:.1f}%)")
    
    # 策略表现分析
    print(f"\n📈 策略表现分析:")
    
    # 计算月度收益率
    detailed_results['date'] = pd.to_datetime(detailed_results['date'])
    detailed_results['year_month'] = detailed_results['date'].dt.to_period('M')
    
    # 按月统计
    monthly_stats = detailed_results.groupby('year_month').agg({
        'capital': ['first', 'last'],
        'benchmark_capital': ['first', 'last']
    }).round(2)
    
    monthly_stats.columns = ['strategy_start', 'strategy_end', 'benchmark_start', 'benchmark_end']
    monthly_stats['strategy_return'] = (monthly_stats['strategy_end'] / monthly_stats['strategy_start'] - 1) * 100
    monthly_stats['benchmark_return'] = (monthly_stats['benchmark_end'] / monthly_stats['benchmark_start'] - 1) * 100
    monthly_stats['excess_return'] = monthly_stats['strategy_return'] - monthly_stats['benchmark_return']
    
    # 月度统计
    positive_months = len(monthly_stats[monthly_stats['strategy_return'] > 0])
    total_months = len(monthly_stats)
    outperform_months = len(monthly_stats[monthly_stats['excess_return'] > 0])
    
    print(f"  • 盈利月份: {positive_months}/{total_months} ({positive_months/total_months*100:.1f}%)")
    print(f"  • 跑赢基准月份: {outperform_months}/{total_months} ({outperform_months/total_months*100:.1f}%)")
    print(f"  • 平均月收益率: {monthly_stats['strategy_return'].mean():.2f}%")
    print(f"  • 月收益率标准差: {monthly_stats['strategy_return'].std():.2f}%")
    print(f"  • 最佳月份收益: {monthly_stats['strategy_return'].max():.2f}%")
    print(f"  • 最差月份收益: {monthly_stats['strategy_return'].min():.2f}%")
    
    # 年度表现
    print(f"\n📅 年度表现:")
    detailed_results['year'] = detailed_results['date'].dt.year
    yearly_stats = detailed_results.groupby('year').agg({
        'capital': ['first', 'last'],
        'benchmark_capital': ['first', 'last']
    }).round(2)
    
    yearly_stats.columns = ['strategy_start', 'strategy_end', 'benchmark_start', 'benchmark_end']
    yearly_stats['strategy_return'] = (yearly_stats['strategy_end'] / yearly_stats['strategy_start'] - 1) * 100
    yearly_stats['benchmark_return'] = (yearly_stats['benchmark_end'] / yearly_stats['benchmark_start'] - 1) * 100
    yearly_stats['excess_return'] = yearly_stats['strategy_return'] - yearly_stats['benchmark_return']
    
    for year, row in yearly_stats.iterrows():
        print(f"  • {year}年: 策略 {row['strategy_return']:6.1f}%, 基准 {row['benchmark_return']:6.1f}%, 超额 {row['excess_return']:6.1f}%")
    
    # 持仓切换分析
    print(f"\n🔄 持仓切换分析:")
    position_changes = (detailed_results['position'] != detailed_results['position'].shift(1)).sum()
    print(f"  • 总切换次数: {position_changes} 次")
    print(f"  • 平均持仓天数: {len(detailed_results) / position_changes:.1f} 天")
    
    # 最佳和最差表现日期
    best_day = detailed_results.loc[detailed_results['capital'].idxmax()]
    worst_drawdown_idx = (detailed_results['capital'] / detailed_results['capital'].cummax() - 1).idxmin()
    worst_day = detailed_results.loc[worst_drawdown_idx]
    
    print(f"\n🏆 关键时点:")
    print(f"  • 资金最高点: {best_day['date'].strftime('%Y-%m-%d')}, 资金: {best_day['capital']:,.0f} USDT")
    print(f"  • 最大回撤点: {worst_day['date'].strftime('%Y-%m-%d')}, 回撤: {(worst_day['capital'] / detailed_results['capital'].cummax().loc[worst_drawdown_idx] - 1)*100:.2f}%")
    
    # 策略评价
    print(f"\n📝 策略评价:")
    if performance['excess_return'] > 0:
        print(f"  ✅ 策略跑赢基准 {performance['excess_return']:.2%}")
    else:
        print(f"  ❌ 策略跑输基准 {abs(performance['excess_return']):.2%}")
    
    if performance['strategy_sharpe'] > performance['benchmark_sharpe']:
        print(f"  ✅ 策略夏普比率更优 ({performance['strategy_sharpe']:.3f} vs {performance['benchmark_sharpe']:.3f})")
    else:
        print(f"  ❌ 基准夏普比率更优 ({performance['benchmark_sharpe']:.3f} vs {performance['strategy_sharpe']:.3f})")
    
    if abs(performance['max_drawdown']) < abs(performance['benchmark_max_drawdown']):
        print(f"  ✅ 策略回撤控制更好 ({performance['max_drawdown']:.2%} vs {performance['benchmark_max_drawdown']:.2%})")
    else:
        print(f"  ❌ 基准回撤控制更好 ({performance['benchmark_max_drawdown']:.2%} vs {performance['max_drawdown']:.2%})")
    
    # 总结
    print(f"\n🎯 总结:")
    print(f"  • 该BTC-ETH选优策略在{performance['years']:.1f}年的回测中表现如下：")
    print(f"  • 年化收益率 {performance['strategy_annual_return']:.2%}，略低于等权重基准的 {performance['benchmark_annual_return']:.2%}")
    print(f"  • 但风险调整后收益（夏普比率）更优：{performance['strategy_sharpe']:.3f} vs {performance['benchmark_sharpe']:.3f}")
    print(f"  • 最大回撤控制更好：{performance['max_drawdown']:.2%} vs {performance['benchmark_max_drawdown']:.2%}")
    print(f"  • 持仓分布相对均衡：BTC {performance['position_stats']['BTCUSDT']}天，ETH {performance['position_stats']['ETHUSDT']}天")
    
    if performance['strategy_sharpe'] > performance['benchmark_sharpe'] and abs(performance['max_drawdown']) < abs(performance['benchmark_max_drawdown']):
        print(f"  • 🌟 综合评价：策略在风险控制方面表现优秀，适合风险偏好较低的投资者")
    else:
        print(f"  • 📊 综合评价：策略表现中等，需要进一步优化以提升收益能力")
    
    print(f"\n📁 详细结果和图表已保存在: {results_dir}")
    print(f"✅ 分析完成！")

if __name__ == "__main__":
    analyze_btc_eth_results()
