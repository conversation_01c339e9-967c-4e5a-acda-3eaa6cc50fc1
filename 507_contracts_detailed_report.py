#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
507合约每日实体涨跌幅度详细报告生成器

功能：
1. 读取507合约分析数据
2. 生成详细的文本报告
3. 包含ASCII图表和统计分析
4. 提供投资建议和风险提示

作者：加密货币量化交易系统
日期：2025年1月28日
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def create_ascii_histogram(data, title, bins=20, width=50):
    """创建ASCII直方图"""
    hist, bin_edges = np.histogram(data, bins=bins)
    max_count = max(hist)
    
    result = f"\n{title}\n" + "=" * len(title) + "\n"
    
    for i in range(len(hist)):
        bin_start = bin_edges[i]
        bin_end = bin_edges[i + 1]
        count = hist[i]
        
        # 计算条形长度
        bar_length = int((count / max_count) * width) if max_count > 0 else 0
        bar = "█" * bar_length
        
        result += f"{bin_start:6.2f} - {bin_end:6.2f} |{bar:<{width}} {count:3d}\n"
    
    return result

def analyze_507_contracts():
    """分析507合约数据并生成详细报告"""
    
    # 查找最新的CSV文件
    csv_files = [f for f in os.listdir('.') if f.startswith('507_contracts_daily_analysis_') and f.endswith('.csv')]
    if not csv_files:
        print("❌ 未找到507合约分析数据文件")
        return
    
    latest_file = sorted(csv_files)[-1]
    print(f"📊 读取数据文件: {latest_file}")
    
    # 读取数据
    df = pd.read_csv(latest_file, encoding='utf-8-sig')
    
    # 生成详细报告
    report_content = f"""
# 507合约每日实体涨跌幅度详细分析报告

## 📊 报告概览
- **生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **数据来源**: 币安官方期货API
- **数据文件**: {latest_file}
- **分析合约数**: {len(df)}
- **数据日期**: {df['date'].iloc[0]}

## 🎯 核心发现

### 市场整体表现
- **阳线合约**: {len(df[df['candle_type'] == '阳线'])} 个 ({len(df[df['candle_type'] == '阳线'])/len(df)*100:.1f}%)
- **阴线合约**: {len(df[df['candle_type'] == '阴线'])} 个 ({len(df[df['candle_type'] == '阴线'])/len(df)*100:.1f}%)
- **十字星合约**: {len(df[df['candle_type'] == '十字星'])} 个 ({len(df[df['candle_type'] == '十字星'])/len(df)*100:.1f}%)

### 市场情绪分析
"""
    
    yang_ratio = len(df[df['candle_type'] == '阳线'])/len(df)*100
    if yang_ratio > 55:
        sentiment = "强烈看多"
        sentiment_desc = "市场呈现明显的上涨趋势，多头力量占主导地位"
    elif yang_ratio > 50:
        sentiment = "偏多"
        sentiment_desc = "市场略偏乐观，但需要关注后续走势"
    elif yang_ratio > 45:
        sentiment = "中性偏多"
        sentiment_desc = "市场处于相对平衡状态，略偏乐观"
    elif yang_ratio > 40:
        sentiment = "中性偏空"
        sentiment_desc = "市场情绪偏谨慎，空头力量略强"
    else:
        sentiment = "偏空"
        sentiment_desc = "市场呈现下跌趋势，空头力量占主导"
    
    report_content += f"**市场情绪**: {sentiment} ({sentiment_desc})\n\n"
    
    # 实体大小分析
    body_stats = df['body_size_pct'].describe()
    report_content += f"""
## 📏 实体大小分析

### 统计数据
- **平均实体大小**: {body_stats['mean']:.3f}%
- **中位数**: {body_stats['50%']:.3f}%
- **标准差**: {body_stats['std']:.3f}%
- **最大涨幅**: {body_stats['max']:.3f}%
- **最大跌幅**: {body_stats['min']:.3f}%
- **25%分位数**: {body_stats['25%']:.3f}%
- **75%分位数**: {body_stats['75%']:.3f}%

### 实体大小分布区间
"""
    
    # 实体大小分布统计
    ranges = [
        (-float('inf'), -3, "大幅下跌"),
        (-3, -1, "中等下跌"),
        (-1, -0.5, "小幅下跌"),
        (-0.5, 0.5, "横盘整理"),
        (0.5, 1, "小幅上涨"),
        (1, 3, "中等上涨"),
        (3, float('inf'), "大幅上涨")
    ]
    
    for min_val, max_val, desc in ranges:
        if min_val == -float('inf'):
            count = len(df[df['body_size_pct'] < max_val])
        elif max_val == float('inf'):
            count = len(df[df['body_size_pct'] >= min_val])
        else:
            count = len(df[(df['body_size_pct'] >= min_val) & (df['body_size_pct'] < max_val)])
        
        percentage = count / len(df) * 100
        report_content += f"- **{desc}**: {count} 个合约 ({percentage:.1f}%)\n"
    
    # 振幅分析
    range_stats = df['total_range_pct'].describe()
    report_content += f"""

## 📊 振幅分析

### 统计数据
- **平均振幅**: {range_stats['mean']:.3f}%
- **中位数振幅**: {range_stats['50%']:.3f}%
- **标准差**: {range_stats['std']:.3f}%
- **最大振幅**: {range_stats['max']:.3f}%
- **最小振幅**: {range_stats['min']:.3f}%

### 振幅分布区间
"""
    
    # 振幅分布统计
    range_intervals = [
        (0, 0.5, "极低波动"),
        (0.5, 1, "低波动"),
        (1, 2, "中等波动"),
        (2, 5, "高波动"),
        (5, float('inf'), "极高波动")
    ]
    
    for min_val, max_val, desc in range_intervals:
        if max_val == float('inf'):
            count = len(df[df['total_range_pct'] >= min_val])
        else:
            count = len(df[(df['total_range_pct'] >= min_val) & (df['total_range_pct'] < max_val)])
        
        percentage = count / len(df) * 100
        report_content += f"- **{desc}**: {count} 个合约 ({percentage:.1f}%)\n"
    
    # 极值合约分析
    report_content += f"""

## 🏆 极值合约分析

### 🔝 最大涨幅合约 (Top 15)
"""
    
    top_gainers = df.nlargest(15, 'body_size_pct')
    for idx, (_, row) in enumerate(top_gainers.iterrows(), 1):
        report_content += f"{idx:2d}. **{row['symbol']}**: +{row['body_size_pct']:.3f}% (振幅: {row['total_range_pct']:.3f}%, 成交量: {row['volume']:,.0f})\n"
    
    report_content += f"""

### 🔻 最大跌幅合约 (Top 15)
"""
    
    top_losers = df.nsmallest(15, 'body_size_pct')
    for idx, (_, row) in enumerate(top_losers.iterrows(), 1):
        report_content += f"{idx:2d}. **{row['symbol']}**: {row['body_size_pct']:.3f}% (振幅: {row['total_range_pct']:.3f}%, 成交量: {row['volume']:,.0f})\n"
    
    report_content += f"""

### 📈 最大振幅合约 (Top 15)
"""
    
    top_ranges = df.nlargest(15, 'total_range_pct')
    for idx, (_, row) in enumerate(top_ranges.iterrows(), 1):
        report_content += f"{idx:2d}. **{row['symbol']}**: {row['total_range_pct']:.3f}% (实体: {row['body_size_pct']:.3f}%, 成交量: {row['volume']:,.0f})\n"
    
    # 成交量分析
    volume_stats = df['volume'].describe()
    report_content += f"""

## 💰 成交量分析

### 统计数据
- **平均成交量**: {volume_stats['mean']:,.0f}
- **中位数成交量**: {volume_stats['50%']:,.0f}
- **最大成交量**: {volume_stats['max']:,.0f}
- **最小成交量**: {volume_stats['min']:,.0f}

### 高成交量合约 (Top 10)
"""
    
    top_volumes = df.nlargest(10, 'volume')
    for idx, (_, row) in enumerate(top_volumes.iterrows(), 1):
        report_content += f"{idx:2d}. **{row['symbol']}**: {row['volume']:,.0f} (实体: {row['body_size_pct']:.3f}%, 振幅: {row['total_range_pct']:.3f}%)\n"
    
    # 投资建议
    report_content += f"""

## 💡 投资建议与风险提示

### 🎯 短线交易机会
基于当前数据分析，以下合约可能存在短线交易机会：

#### 高涨幅高成交量合约
"""
    
    # 筛选高涨幅高成交量合约
    high_gain_volume = df[(df['body_size_pct'] > 2) & (df['volume'] > df['volume'].median())]
    if len(high_gain_volume) > 0:
        for _, row in high_gain_volume.head(5).iterrows():
            report_content += f"- **{row['symbol']}**: 涨幅 {row['body_size_pct']:.3f}%, 成交量 {row['volume']:,.0f}\n"
    else:
        report_content += "- 当前无明显的高涨幅高成交量合约\n"
    
    report_content += f"""

#### 高振幅合约（适合波段交易）
"""
    
    # 筛选高振幅合约
    high_volatility = df[df['total_range_pct'] > 5].sort_values('total_range_pct', ascending=False)
    for _, row in high_volatility.head(5).iterrows():
        report_content += f"- **{row['symbol']}**: 振幅 {row['total_range_pct']:.3f}%, 实体 {row['body_size_pct']:.3f}%\n"
    
    # 风险提示
    avg_volatility = df['total_range_pct'].mean()
    high_vol_count = len(df[df['total_range_pct'] > 5])
    
    if avg_volatility > 2:
        risk_level = "高风险"
        risk_desc = "市场波动较大，建议控制仓位"
    elif avg_volatility > 1:
        risk_level = "中等风险"
        risk_desc = "市场波动适中，注意风险控制"
    else:
        risk_level = "低风险"
        risk_desc = "市场波动较小，相对安全"
    
    report_content += f"""

### ⚠️ 风险提示
- **市场风险等级**: {risk_level}
- **风险描述**: {risk_desc}
- **平均市场振幅**: {avg_volatility:.3f}%
- **高波动合约数量**: {high_vol_count} 个 ({high_vol_count/len(df)*100:.1f}%)

### 📋 操作建议
1. **仓位控制**: 建议单个合约仓位不超过总资金的5%
2. **止损设置**: 建议设置2-3%的止损位
3. **关注重点**: 优先关注成交量大、振幅适中的合约
4. **时间选择**: 避免在市场极度波动时进行大额交易

## 📈 技术分析要点

### K线形态分析
- **强势阳线**: {len(df[(df['candle_type'] == '阳线') & (df['body_size_pct'] > 2)])} 个
- **强势阴线**: {len(df[(df['candle_type'] == '阴线') & (df['body_size_pct'] < -2)])} 个
- **长上影线**: {len(df[df['body_ratio'] < 0.5])} 个 (可能存在上方压力)
- **实体饱满**: {len(df[df['body_ratio'] > 0.7])} 个 (趋势相对明确)

### 市场结构分析
- **趋势性合约**: {len(df[abs(df['body_size_pct']) > 1])} 个 ({len(df[abs(df['body_size_pct']) > 1])/len(df)*100:.1f}%)
- **震荡性合约**: {len(df[abs(df['body_size_pct']) <= 1])} 个 ({len(df[abs(df['body_size_pct']) <= 1])/len(df)*100:.1f}%)

---

**免责声明**: 本报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。

**报告生成时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}  
**数据来源**: 币安官方期货API  
**分析工具**: 507合约每日实体涨跌幅度分析器
"""
    
    # 保存报告
    report_file = f"507_contracts_detailed_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"✅ 详细报告已生成: {report_file}")
    
    # 显示关键信息
    print(f"\n📊 507合约每日实体涨跌幅度分析摘要")
    print(f"=" * 50)
    print(f"📅 数据日期: {df['date'].iloc[0]}")
    print(f"📈 分析合约: {len(df)} 个")
    print(f"🎯 市场情绪: {sentiment}")
    print(f"📏 平均实体: {body_stats['mean']:.3f}%")
    print(f"📊 平均振幅: {range_stats['mean']:.3f}%")
    print(f"🔝 最大涨幅: {body_stats['max']:.3f}% ({df.loc[df['body_size_pct'].idxmax(), 'symbol']})")
    print(f"🔻 最大跌幅: {body_stats['min']:.3f}% ({df.loc[df['body_size_pct'].idxmin(), 'symbol']})")
    print(f"📈 最大振幅: {range_stats['max']:.3f}% ({df.loc[df['total_range_pct'].idxmax(), 'symbol']})")
    
    return report_file

if __name__ == "__main__":
    print("🚀 生成507合约详细分析报告...")
    report_file = analyze_507_contracts()
    if report_file:
        print(f"\n✅ 报告生成完成: {report_file}")
    else:
        print("\n❌ 报告生成失败")
