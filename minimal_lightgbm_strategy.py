# -*- coding: utf-8 -*-
"""
最简单的LightGBM预测BTC和ETH涨跌幅选优交易策略
如果LightGBM不可用，自动降级为随机森林
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
import os

# 设置中文字体和忽略警告
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

# 尝试导入LightGBM，如果失败则使用随机森林
try:
    import lightgbm as lgb
    USE_LIGHTGBM = True
    print("✅ 使用LightGBM模型")
except ImportError:
    from sklearn.ensemble import RandomForestRegressor
    USE_LIGHTGBM = False
    print("⚠️ LightGBM不可用，使用随机森林替代")

class MinimalLightGBMStrategy:
    """最简单的LightGBM预测策略"""
    
    def __init__(self):
        """初始化"""
        self.data_dir = "data/btc_eth_data"
        self.results_dir = "data/minimal_lightgbm_results"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 策略参数
        self.initial_capital = 100000  # 初始资金
        self.transaction_cost = 0.001  # 交易费用
        self.train_window = 100  # 训练窗口
        
        # 模型参数
        if USE_LIGHTGBM:
            self.model_params = {
                'objective': 'regression',
                'metric': 'rmse',
                'boosting_type': 'gbdt',
                'num_leaves': 31,
                'learning_rate': 0.1,
                'feature_fraction': 0.9,
                'verbose': -1,
                'random_state': 42
            }
        else:
            self.model_params = {
                'n_estimators': 50,
                'max_depth': 10,
                'random_state': 42,
                'n_jobs': -1
            }
    
    def load_data(self):
        """加载数据"""
        print("📊 加载BTC和ETH数据...")
        
        combined_file = os.path.join(self.data_dir, "btc_eth_combined.csv")
        df = pd.read_csv(combined_file)
        df['date'] = pd.to_datetime(df['date'])
        
        print(f"✅ 数据加载完成: {len(df)} 条记录")
        return df
    
    def create_features(self, df):
        """创建简单特征"""
        print("🔧 创建特征...")
        
        features_df = []
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_df = df[df['symbol'] == symbol].copy().sort_values('date').reset_index(drop=True)
            
            # 基础特征
            symbol_df['returns'] = symbol_df['close'].pct_change()
            symbol_df['volume_change'] = symbol_df['volume'].pct_change()
            
            # 简单技术指标
            symbol_df['ma5'] = symbol_df['close'].rolling(5).mean()
            symbol_df['ma20'] = symbol_df['close'].rolling(20).mean()
            symbol_df['ma_ratio'] = symbol_df['ma5'] / symbol_df['ma20']
            
            # 价格位置
            symbol_df['price_pos'] = (symbol_df['close'] - symbol_df['close'].rolling(10).min()) / \
                                   (symbol_df['close'].rolling(10).max() - symbol_df['close'].rolling(10).min())
            
            # 滞后特征
            for lag in [1, 2, 3, 5]:
                symbol_df[f'returns_lag_{lag}'] = symbol_df['returns'].shift(lag)
                symbol_df[f'volume_lag_{lag}'] = symbol_df['volume_change'].shift(lag)
            
            # 目标变量：下一日收益率
            symbol_df['target'] = symbol_df['returns'].shift(-1)
            
            features_df.append(symbol_df)
        
        combined_df = pd.concat(features_df, ignore_index=True)
        combined_df = combined_df.sort_values(['symbol', 'date']).reset_index(drop=True)
        
        print(f"✅ 特征创建完成")
        return combined_df
    
    def train_model(self, X_train, y_train):
        """训练模型"""
        if USE_LIGHTGBM:
            # 使用LightGBM
            train_data = lgb.Dataset(X_train, label=y_train)
            model = lgb.train(
                self.model_params,
                train_data,
                num_boost_round=100,
                callbacks=[lgb.log_evaluation(0)]
            )
        else:
            # 使用随机森林
            model = RandomForestRegressor(**self.model_params)
            model.fit(X_train, y_train)
        
        return model
    
    def predict_model(self, model, X_test):
        """模型预测"""
        if USE_LIGHTGBM:
            return model.predict(X_test)
        else:
            return model.predict(X_test)
    
    def rolling_predict(self, df):
        """滚动预测"""
        print("🤖 执行滚动预测...")
        
        # 选择特征
        feature_cols = [
            'ma_ratio', 'price_pos',
            'returns_lag_1', 'returns_lag_2', 'returns_lag_3', 'returns_lag_5',
            'volume_lag_1', 'volume_lag_2', 'volume_lag_3', 'volume_lag_5'
        ]
        
        predictions = {}
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            print(f"📈 预测 {symbol}...")
            
            symbol_df = df[df['symbol'] == symbol].copy().sort_values('date').reset_index(drop=True)
            symbol_df = symbol_df.dropna()
            
            pred_results = []
            
            # 滚动预测
            for i in range(self.train_window, len(symbol_df) - 1):
                # 训练数据
                train_start = max(0, i - self.train_window)
                train_end = i
                
                train_data = symbol_df.iloc[train_start:train_end]
                X_train = train_data[feature_cols].values
                y_train = train_data['target'].values
                
                # 移除NaN
                valid_idx = ~(np.isnan(X_train).any(axis=1) | np.isnan(y_train))
                X_train = X_train[valid_idx]
                y_train = y_train[valid_idx]
                
                if len(X_train) < 20:
                    continue
                
                # 训练模型
                model = self.train_model(X_train, y_train)
                
                # 预测
                X_test = symbol_df.iloc[i][feature_cols].values.reshape(1, -1)
                if not np.isnan(X_test).any():
                    pred = self.predict_model(model, X_test)[0]
                    actual = symbol_df.iloc[i]['target']
                    
                    pred_results.append({
                        'date': symbol_df.iloc[i]['date'],
                        'prediction': pred,
                        'actual': actual
                    })
            
            predictions[symbol] = pd.DataFrame(pred_results)
            print(f"  • {symbol} 预测完成: {len(pred_results)} 个预测")
        
        return predictions
    
    def backtest_strategy(self, predictions):
        """回测策略"""
        print("📊 执行策略回测...")
        
        # 获取共同日期
        btc_dates = set(predictions['BTCUSDT']['date'])
        eth_dates = set(predictions['ETHUSDT']['date'])
        common_dates = sorted(btc_dates & eth_dates)
        
        print(f"📅 共同交易日期: {len(common_dates)} 天")
        
        results = []
        capital = self.initial_capital
        benchmark_capital = self.initial_capital
        position = None
        
        for date in common_dates:
            # 获取预测数据
            btc_data = predictions['BTCUSDT'][predictions['BTCUSDT']['date'] == date]
            eth_data = predictions['ETHUSDT'][predictions['ETHUSDT']['date'] == date]
            
            if len(btc_data) == 0 or len(eth_data) == 0:
                continue
            
            btc_pred = btc_data.iloc[0]['prediction']
            eth_pred = eth_data.iloc[0]['prediction']
            btc_actual = btc_data.iloc[0]['actual']
            eth_actual = eth_data.iloc[0]['actual']
            
            # 策略逻辑：选择预测收益率更高的币种
            if btc_pred > eth_pred and btc_pred > 0.003:  # 阈值0.3%
                new_position = 'BTCUSDT'
                selected_return = btc_actual
            elif eth_pred > btc_pred and eth_pred > 0.003:
                new_position = 'ETHUSDT'
                selected_return = eth_actual
            else:
                new_position = None
                selected_return = 0
            
            # 计算收益
            if new_position is not None:
                if position != new_position:
                    # 换仓或开仓
                    net_return = selected_return - self.transaction_cost
                    if position is not None:
                        net_return -= self.transaction_cost * 0.5
                else:
                    # 继续持有
                    net_return = selected_return - self.transaction_cost * 0.1
                
                capital *= (1 + net_return)
            
            position = new_position
            
            # 基准收益
            benchmark_return = (btc_actual + eth_actual) / 2
            if not pd.isna(benchmark_return):
                benchmark_capital *= (1 + benchmark_return - self.transaction_cost * 0.1)
            
            results.append({
                'date': date,
                'capital': capital,
                'benchmark_capital': benchmark_capital,
                'position': position,
                'btc_pred': btc_pred,
                'eth_pred': eth_pred,
                'btc_actual': btc_actual,
                'eth_actual': eth_actual
            })
        
        return pd.DataFrame(results)
    
    def analyze_and_plot(self, results_df):
        """分析并绘制结果"""
        print("📈 分析策略表现...")
        
        # 计算指标
        strategy_return = (results_df['capital'].iloc[-1] / self.initial_capital) - 1
        benchmark_return = (results_df['benchmark_capital'].iloc[-1] / self.initial_capital) - 1
        
        days = len(results_df)
        years = days / 365
        strategy_annual = (1 + strategy_return) ** (1 / years) - 1
        benchmark_annual = (1 + benchmark_return) ** (1 / years) - 1
        
        # 最大回撤
        results_df['strategy_peak'] = results_df['capital'].cummax()
        results_df['strategy_dd'] = (results_df['capital'] - results_df['strategy_peak']) / results_df['strategy_peak']
        max_dd = results_df['strategy_dd'].min()
        
        # 胜率
        results_df['strategy_returns'] = results_df['capital'].pct_change()
        win_rate = (results_df['strategy_returns'] > 0).mean()
        
        # 预测准确性
        btc_corr = np.corrcoef(results_df['btc_pred'].dropna(), results_df['btc_actual'].dropna())[0,1] if len(results_df['btc_pred'].dropna()) > 1 else 0
        eth_corr = np.corrcoef(results_df['eth_pred'].dropna(), results_df['eth_actual'].dropna())[0,1] if len(results_df['eth_pred'].dropna()) > 1 else 0
        
        # 绘制结果
        print("📊 生成图表...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        model_name = "LightGBM" if USE_LIGHTGBM else "随机森林"
        fig.suptitle(f'{model_name}预测策略回测结果', fontsize=16, fontweight='bold')
        
        # 资金曲线
        ax1 = axes[0, 0]
        ax1.plot(results_df['date'], results_df['capital'], label=f'{model_name}策略', linewidth=2, color='blue')
        ax1.plot(results_df['date'], results_df['benchmark_capital'], label='等权重基准', linewidth=2, color='red', alpha=0.7)
        ax1.set_title('资金曲线对比')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 添加收益率标注
        ax1.text(0.02, 0.98, f'策略收益: {strategy_return:.2%}', transform=ax1.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        ax1.text(0.02, 0.90, f'基准收益: {benchmark_return:.2%}', transform=ax1.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
        
        # 回撤
        ax2 = axes[0, 1]
        ax2.fill_between(results_df['date'], results_df['strategy_dd'], 0, alpha=0.3, color='red')
        ax2.set_title(f'策略回撤 (最大: {max_dd:.2%})')
        ax2.set_ylabel('回撤比例')
        ax2.grid(True, alpha=0.3)
        
        # 预测准确性
        ax3 = axes[1, 0]
        bars = ax3.bar(['BTC预测相关性', 'ETH预测相关性'], [btc_corr, eth_corr], 
                      color=['orange', 'purple'], alpha=0.7)
        ax3.set_title('预测准确性')
        ax3.set_ylabel('相关系数')
        ax3.set_ylim(-0.3, 0.3)
        ax3.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars, [btc_corr, eth_corr]):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{value:.3f}', ha='center', va='bottom')
        
        # 持仓分布
        ax4 = axes[1, 1]
        position_counts = results_df['position'].value_counts()
        if len(position_counts) > 0:
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
            ax4.pie(position_counts.values, labels=position_counts.index, 
                   autopct='%1.1f%%', colors=colors[:len(position_counts)])
            ax4.set_title('持仓分布')
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = os.path.join(self.results_dir, 'minimal_lightgbm_results.png')
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.show()
        
        # 显示结果
        print(f"\n🎉 {model_name}预测策略回测完成！")
        print("=" * 60)
        print(f"📈 策略表现:")
        print(f"  • 使用模型: {model_name}")
        print(f"  • 回测期间: {years:.1f} 年")
        print(f"  • 策略总收益: {strategy_return:.2%}")
        print(f"  • 基准总收益: {benchmark_return:.2%}")
        print(f"  • 超额收益: {strategy_return - benchmark_return:.2%}")
        print(f"  • 年化收益率: {strategy_annual:.2%}")
        print(f"  • 最大回撤: {max_dd:.2%}")
        print(f"  • 胜率: {win_rate:.2%}")
        print(f"  • 最终资金: {results_df['capital'].iloc[-1]:,.0f} USDT")
        print(f"\n🎯 预测准确性:")
        print(f"  • BTC预测相关性: {btc_corr:.3f}")
        print(f"  • ETH预测相关性: {eth_corr:.3f}")
        print(f"📁 结果保存在: {self.results_dir}")
        
        return True
    
    def run(self):
        """运行完整流程"""
        print("🚀 开始最简单的LightGBM预测策略回测...")
        print("=" * 60)
        
        try:
            # 1. 加载数据
            df = self.load_data()
            
            # 2. 创建特征
            features_df = self.create_features(df)
            
            # 3. 滚动预测
            predictions = self.rolling_predict(features_df)
            
            # 4. 回测策略
            results_df = self.backtest_strategy(predictions)
            
            # 5. 分析并绘制
            success = self.analyze_and_plot(results_df)
            
            return success
            
        except Exception as e:
            print(f"❌ 策略回测失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    strategy = MinimalLightGBMStrategy()
    success = strategy.run()
    
    if success:
        print("\n✅ 程序执行成功")
    else:
        print("\n❌ 程序执行失败")

if __name__ == "__main__":
    main()
