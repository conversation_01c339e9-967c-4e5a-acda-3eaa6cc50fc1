import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 数据加载与预处理
def preprocess_data():
    """
    加载并预处理比特币USDT永续合约日K线数据
    返回：
    processed_df: 预处理后的DataFrame
    """
    # 读取比特币USDT永续合约数据
    csv_path = "data/perpetual_historical/BTCUSDT_historical.csv"
    print(f"📊 加载比特币USDT永续合约数据: {csv_path}")

    try:
        df = pd.read_csv(csv_path)
        print(f"✅ 数据加载成功: {len(df)} 条记录")
    except FileNotFoundError:
        print(f"❌ 文件不存在: {csv_path}")
        print("尝试使用备用数据源...")
        # 备用数据源
        csv_path = "data/btc_eth_data/BTCUSDT_historical.csv"
        df = pd.read_csv(csv_path)
        print(f"✅ 备用数据加载成功: {len(df)} 条记录")

    # 数据预处理
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)

    print(f"📅 数据时间范围: {df['date'].min()} 到 {df['date'].max()}")
    print(f"💰 价格范围: ${df['close'].min():.2f} - ${df['close'].max():.2f}")

    return df

def create_features(df):
    """
    创建技术指标特征
    """
    print("🔧 创建技术指标特征...")

    # 基础价格特征
    df['returns'] = df['close'].pct_change()  # 日收益率
    df['body'] = (df['close'] - df['open']) / df['open']  # 实体涨跌幅度
    df['upper_shadow'] = (df['high'] - df[['open', 'close']].max(axis=1)) / df['close']  # 上影线
    df['lower_shadow'] = (df[['open', 'close']].min(axis=1) - df['low']) / df['close']  # 下影线

    # 移动平均线
    df['sma_5'] = df['close'].rolling(window=5).mean()  # 5日简单均线
    df['sma_10'] = df['close'].rolling(window=10).mean()  # 10日简单均线
    df['sma_20'] = df['close'].rolling(window=20).mean()  # 20日简单均线
    df['ema_12'] = df['close'].ewm(span=12).mean()  # 12日指数均线
    df['ema_26'] = df['close'].ewm(span=26).mean()  # 26日指数均线

    # 价格相对位置
    df['price_sma5_ratio'] = df['close'] / df['sma_5']
    df['price_sma20_ratio'] = df['close'] / df['sma_20']

    # 技术指标
    df['rsi'] = compute_rsi(df['close'])  # RSI指标
    df['volatility'] = (df['high'] - df['low']) / df['close']  # 波动率
    df['volume_sma'] = df['volume'].rolling(window=10).mean()  # 成交量均线
    df['volume_ratio'] = df['volume'] / df['volume_sma']  # 成交量比率

    # 滞后特征
    for lag in [1, 2, 3, 5]:
        df[f'returns_lag_{lag}'] = df['returns'].shift(lag)
        df[f'volume_lag_{lag}'] = df['volume'].shift(lag)

    # 创建目标变量（未来第2天的收益率）
    df['target'] = df['returns'].shift(-2)

    print("✅ 特征创建完成")
    return df

def compute_rsi(series, period=14):
    """计算相对强弱指标(RSI)"""
    delta = series.diff().dropna()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)

    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()

    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def run_backtest_strategy(model, X_test, y_test, test_df):
    """
    运行回测策略
    """
    print("📊 执行策略回测...")

    # 获取预测结果
    predictions = model.predict(X_test)

    # 策略参数
    initial_capital = 100000  # 初始资金
    transaction_cost = 0.001  # 交易费用 0.1%
    threshold = 0.001  # 预测阈值 0.1%

    # 初始化
    capital = initial_capital
    position = 0  # 0: 空仓, 1: 多头, -1: 空头
    trades = []
    equity_curve = []

    # 重置索引确保对齐
    test_df_reset = test_df.reset_index(drop=True)

    for i in range(len(predictions)):
        if i >= len(test_df_reset):
            break

        pred = predictions[i]
        actual = y_test.iloc[i]
        current_price = test_df_reset.iloc[i]['close']
        current_date = test_df_reset.iloc[i]['date']

        # 策略逻辑
        new_position = 0
        if pred > threshold:  # 预测上涨
            new_position = 1
        elif pred < -threshold:  # 预测下跌
            new_position = -1

        # 执行交易
        if new_position != position:
            # 平仓
            if position != 0:
                if position == 1:  # 平多头
                    capital *= (1 + actual - transaction_cost)
                else:  # 平空头
                    capital *= (1 - actual - transaction_cost)

                trades.append({
                    'date': current_date,
                    'action': 'close',
                    'position': position,
                    'price': current_price,
                    'return': actual,
                    'capital': capital
                })

            # 开仓
            if new_position != 0:
                capital *= (1 - transaction_cost)  # 开仓手续费
                trades.append({
                    'date': current_date,
                    'action': 'open',
                    'position': new_position,
                    'price': current_price,
                    'prediction': pred,
                    'capital': capital
                })

            position = new_position

        # 记录资金曲线
        equity_curve.append({
            'date': current_date,
            'capital': capital,
            'position': position,
            'prediction': pred,
            'actual': actual,
            'price': current_price
        })

    # 最后平仓
    if position != 0 and len(equity_curve) > 0:
        last_actual = y_test.iloc[-1]
        if position == 1:
            capital *= (1 + last_actual - transaction_cost)
        else:
            capital *= (1 - last_actual - transaction_cost)

        equity_curve[-1]['capital'] = capital

    results = {
        'equity_curve': pd.DataFrame(equity_curve),
        'trades': pd.DataFrame(trades),
        'initial_capital': initial_capital,
        'final_capital': capital,
        'total_return': (capital / initial_capital) - 1,
        'num_trades': len(trades)
    }

    print(f"📈 回测完成:")
    print(f"  • 初始资金: ${initial_capital:,.0f}")
    print(f"  • 最终资金: ${capital:,.0f}")
    print(f"  • 总收益率: {results['total_return']:.2%}")
    print(f"  • 交易次数: {results['num_trades']}")

    return results

def plot_equity_curve(results):
    """
    绘制资金曲线和策略分析图表
    """
    print("📊 生成资金曲线图表...")

    equity_df = results['equity_curve']

    # 计算基准收益（买入持有）
    if len(equity_df) > 0:
        initial_price = equity_df.iloc[0]['price']
        equity_df['benchmark_capital'] = results['initial_capital'] * (equity_df['price'] / initial_price)

        # 计算策略指标
        equity_df['strategy_returns'] = equity_df['capital'].pct_change()
        equity_df['benchmark_returns'] = equity_df['benchmark_capital'].pct_change()

        # 计算累计最大值和回撤
        equity_df['strategy_peak'] = equity_df['capital'].cummax()
        equity_df['strategy_drawdown'] = (equity_df['capital'] - equity_df['strategy_peak']) / equity_df['strategy_peak']

        max_drawdown = equity_df['strategy_drawdown'].min()

        # 计算夏普比率
        strategy_sharpe = equity_df['strategy_returns'].mean() / equity_df['strategy_returns'].std() * np.sqrt(252) if equity_df['strategy_returns'].std() > 0 else 0

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('比特币USDT永续合约LightGBM预测策略回测结果', fontsize=16, fontweight='bold')

        # 1. 资金曲线对比
        ax1 = axes[0, 0]
        ax1.plot(equity_df['date'], equity_df['capital'], label='LightGBM策略', linewidth=2, color='blue')
        ax1.plot(equity_df['date'], equity_df['benchmark_capital'], label='买入持有', linewidth=2, color='red', alpha=0.7)
        ax1.set_title('资金曲线对比', fontsize=14, fontweight='bold')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('资金 (USDT)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 添加收益率标注
        strategy_return = results['total_return']
        benchmark_return = (equity_df['benchmark_capital'].iloc[-1] / results['initial_capital']) - 1
        ax1.text(0.02, 0.98, f'策略收益: {strategy_return:.2%}', transform=ax1.transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        ax1.text(0.02, 0.90, f'基准收益: {benchmark_return:.2%}', transform=ax1.transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))

        # 2. 回撤分析
        ax2 = axes[0, 1]
        ax2.fill_between(equity_df['date'], equity_df['strategy_drawdown'], 0, alpha=0.3, color='red')
        ax2.set_title(f'策略回撤 (最大: {max_drawdown:.2%})', fontsize=14, fontweight='bold')
        ax2.set_xlabel('日期')
        ax2.set_ylabel('回撤比例')
        ax2.grid(True, alpha=0.3)

        # 3. 预测vs实际散点图
        ax3 = axes[1, 0]
        ax3.scatter(equity_df['prediction'], equity_df['actual'], alpha=0.6, s=20)
        ax3.plot([-0.1, 0.1], [-0.1, 0.1], 'r--', alpha=0.8)
        correlation = np.corrcoef(equity_df['prediction'].dropna(), equity_df['actual'].dropna())[0,1]
        ax3.set_title(f'预测vs实际 (相关性: {correlation:.3f})', fontsize=14, fontweight='bold')
        ax3.set_xlabel('预测收益率')
        ax3.set_ylabel('实际收益率')
        ax3.grid(True, alpha=0.3)

        # 4. 持仓分布
        ax4 = axes[1, 1]
        position_counts = equity_df['position'].value_counts()
        position_labels = {1: '多头', -1: '空头', 0: '空仓'}
        labels = [position_labels.get(pos, f'位置{pos}') for pos in position_counts.index]
        colors = ['green', 'red', 'gray']

        if len(position_counts) > 0:
            wedges, texts, autotexts = ax4.pie(position_counts.values, labels=labels,
                                              autopct='%1.1f%%', colors=colors[:len(position_counts)])
            ax4.set_title('持仓分布', fontsize=14, fontweight='bold')

        plt.tight_layout()

        # 保存图表
        import os
        os.makedirs("data/btc_lightgbm_results", exist_ok=True)
        chart_file = "data/btc_lightgbm_results/strategy_analysis.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {chart_file}")
        plt.show()

        # 输出详细统计
        print(f"\n📈 策略详细统计:")
        print(f"  • 策略总收益: {strategy_return:.2%}")
        print(f"  • 基准总收益: {benchmark_return:.2%}")
        print(f"  • 超额收益: {strategy_return - benchmark_return:.2%}")
        print(f"  • 最大回撤: {max_drawdown:.2%}")
        print(f"  • 夏普比率: {strategy_sharpe:.2f}")
        print(f"  • 预测相关性: {correlation:.3f}")
        print(f"  • 交易次数: {results['num_trades']}")

        if results['num_trades'] > 0:
            win_rate = len(results['trades'][results['trades']['return'] > 0]) / len(results['trades'][results['trades']['return'].notna()])
            print(f"  • 胜率: {win_rate:.2%}")

    else:
        print("❌ 没有足够的数据生成图表")

def save_daily_predictions(model, X_test, y_test, test_df, feature_cols):
    """
    保存每日预测结果到文件
    """
    print("📝 生成每日预测报告...")

    # 获取预测结果
    predictions = model.predict(X_test)

    # 重置索引确保对齐
    test_df_reset = test_df.reset_index(drop=True)

    # 创建预测结果DataFrame
    prediction_results = []

    for i in range(len(predictions)):
        if i >= len(test_df_reset):
            break

        pred = predictions[i]
        actual = y_test.iloc[i]
        current_date = test_df_reset.iloc[i]['date']
        current_price = test_df_reset.iloc[i]['close']

        # 计算预测信号
        if pred > 0.005:  # 预测上涨超过0.5%
            signal = "强烈看涨"
            signal_code = 2
        elif pred > 0.002:  # 预测上涨超过0.2%
            signal = "看涨"
            signal_code = 1
        elif pred > -0.002:  # 预测在±0.2%之间
            signal = "震荡"
            signal_code = 0
        elif pred > -0.005:  # 预测下跌超过0.2%
            signal = "看跌"
            signal_code = -1
        else:  # 预测下跌超过0.5%
            signal = "强烈看跌"
            signal_code = -2

        # 计算实际信号
        if actual > 0.005:
            actual_signal = "强烈看涨"
            actual_code = 2
        elif actual > 0.002:
            actual_signal = "看涨"
            actual_code = 1
        elif actual > -0.002:
            actual_signal = "震荡"
            actual_code = 0
        elif actual > -0.005:
            actual_signal = "看跌"
            actual_code = -1
        else:
            actual_signal = "强烈看跌"
            actual_code = -2

        # 判断预测准确性
        direction_correct = (pred > 0 and actual > 0) or (pred < 0 and actual < 0) or (abs(pred) <= 0.002 and abs(actual) <= 0.002)
        signal_correct = signal_code == actual_code

        prediction_results.append({
            'date': current_date,
            'price': current_price,
            'predicted_return': pred,
            'actual_return': actual,
            'predicted_signal': signal,
            'actual_signal': actual_signal,
            'direction_correct': direction_correct,
            'signal_correct': signal_correct,
            'abs_error': abs(pred - actual),
            'prediction_confidence': abs(pred)
        })

    # 转换为DataFrame
    predictions_df = pd.DataFrame(prediction_results)

    # 创建保存目录
    import os
    os.makedirs("data/btc_lightgbm_results", exist_ok=True)

    # 保存详细预测结果
    detailed_file = "data/btc_lightgbm_results/daily_predictions_detailed.csv"
    predictions_df.to_csv(detailed_file, index=False, encoding='utf-8-sig')
    print(f"📊 详细预测结果已保存: {detailed_file}")

    # 创建简化版预测报告
    summary_results = []
    for _, row in predictions_df.iterrows():
        summary_results.append({
            '日期': row['date'].strftime('%Y-%m-%d'),
            '收盘价': f"${row['price']:,.2f}",
            '预测收益率': f"{row['predicted_return']:.2%}",
            '实际收益率': f"{row['actual_return']:.2%}",
            '预测信号': row['predicted_signal'],
            '实际信号': row['actual_signal'],
            '方向正确': '✅' if row['direction_correct'] else '❌',
            '信号正确': '✅' if row['signal_correct'] else '❌',
            '预测误差': f"{row['abs_error']:.2%}",
            '预测置信度': f"{row['prediction_confidence']:.2%}"
        })

    summary_df = pd.DataFrame(summary_results)

    # 保存简化版报告
    summary_file = "data/btc_lightgbm_results/daily_predictions_summary.csv"
    summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
    print(f"📋 简化预测报告已保存: {summary_file}")

    # 生成统计报告
    stats_report = generate_prediction_stats(predictions_df)

    # 保存统计报告
    stats_file = "data/btc_lightgbm_results/prediction_statistics.txt"
    with open(stats_file, 'w', encoding='utf-8') as f:
        f.write(stats_report)
    print(f"📈 统计报告已保存: {stats_file}")

    # 显示预测统计
    print("\n📊 预测结果统计:")
    direction_accuracy = predictions_df['direction_correct'].mean()
    signal_accuracy = predictions_df['signal_correct'].mean()
    avg_error = predictions_df['abs_error'].mean()
    avg_confidence = predictions_df['prediction_confidence'].mean()

    print(f"  • 方向准确率: {direction_accuracy:.2%}")
    print(f"  • 信号准确率: {signal_accuracy:.2%}")
    print(f"  • 平均预测误差: {avg_error:.2%}")
    print(f"  • 平均预测置信度: {avg_confidence:.2%}")
    print(f"  • 预测天数: {len(predictions_df)} 天")

    return predictions_df

def generate_prediction_stats(predictions_df):
    """
    生成详细的预测统计报告
    """
    report = "比特币USDT永续合约LightGBM预测统计报告\n"
    report += "=" * 60 + "\n\n"

    # 基本统计
    total_days = len(predictions_df)
    direction_accuracy = predictions_df['direction_correct'].mean()
    signal_accuracy = predictions_df['signal_correct'].mean()
    avg_error = predictions_df['abs_error'].mean()
    avg_confidence = predictions_df['prediction_confidence'].mean()

    report += f"📊 基本统计:\n"
    report += f"  • 预测天数: {total_days} 天\n"
    report += f"  • 方向准确率: {direction_accuracy:.2%}\n"
    report += f"  • 信号准确率: {signal_accuracy:.2%}\n"
    report += f"  • 平均预测误差: {avg_error:.2%}\n"
    report += f"  • 平均预测置信度: {avg_confidence:.2%}\n\n"

    # 信号分布统计
    signal_counts = predictions_df['predicted_signal'].value_counts()
    report += f"📈 预测信号分布:\n"
    for signal, count in signal_counts.items():
        percentage = count / total_days * 100
        report += f"  • {signal}: {count} 次 ({percentage:.1f}%)\n"
    report += "\n"

    # 准确率按信号类型分析
    report += f"🎯 各信号类型准确率:\n"
    for signal in signal_counts.index:
        signal_data = predictions_df[predictions_df['predicted_signal'] == signal]
        if len(signal_data) > 0:
            accuracy = signal_data['signal_correct'].mean()
            report += f"  • {signal}: {accuracy:.2%} ({len(signal_data)} 次预测)\n"
    report += "\n"

    # 预测误差分析
    report += f"📉 预测误差分析:\n"
    report += f"  • 最小误差: {predictions_df['abs_error'].min():.2%}\n"
    report += f"  • 最大误差: {predictions_df['abs_error'].max():.2%}\n"
    report += f"  • 中位数误差: {predictions_df['abs_error'].median():.2%}\n"
    report += f"  • 标准差: {predictions_df['abs_error'].std():.2%}\n\n"

    # 时间段分析
    predictions_df['month'] = pd.to_datetime(predictions_df['date']).dt.month
    monthly_accuracy = predictions_df.groupby('month')['direction_correct'].mean()
    report += f"📅 月度准确率分析:\n"
    for month, accuracy in monthly_accuracy.items():
        month_name = pd.to_datetime(f"2024-{month:02d}-01").strftime('%B')
        report += f"  • {month_name}: {accuracy:.2%}\n"

    return report

# 主程序
def main():
    """主程序"""
    print("🚀 开始比特币USDT永续合约预测分析...")
    print("=" * 60)

    # 参数设置
    TEST_SIZE = 0.2  # 测试集比例
    RANDOM_STATE = 42  # 随机种子

    # 数据预处理
    df = preprocess_data()
    df = create_features(df)

    # 删除包含NaN的行
    df = df.dropna().reset_index(drop=True)
    print(f"📊 清理后数据: {len(df)} 条记录")

    # 选择特征
    feature_cols = [
        'returns', 'body', 'upper_shadow', 'lower_shadow',
        'price_sma5_ratio', 'price_sma20_ratio', 'rsi', 'volatility', 'volume_ratio',
        'returns_lag_1', 'returns_lag_2', 'returns_lag_3', 'returns_lag_5',
        'volume_lag_1', 'volume_lag_2', 'volume_lag_3', 'volume_lag_5'
    ]

    # 划分特征和目标
    X = df[feature_cols]
    y = df['target']

    # 时间序列分割（确保测试集在时间上靠后）
    split_idx = int(len(df) * (1 - TEST_SIZE))
    X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
    y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]

    print(f"📈 训练集: {len(X_train)} 条记录")
    print(f"📊 测试集: {len(X_test)} 条记录")

    # 创建LightGBM数据集
    train_data = lgb.Dataset(X_train, label=y_train)
    test_data = lgb.Dataset(X_test, label=y_test, reference=train_data)

    # 模型参数设置
    params = {
        'objective': 'regression',  # 回归任务
        'metric': 'rmse',  # 评估指标
        'boosting_type': 'gbdt',  # 基学习器类型
        'learning_rate': 0.05,  # 学习率
        'num_leaves': 31,  # 叶节点数
        'max_depth': 5,  # 树深度
        'min_child_samples': 20,  # 叶节点最小样本数
        'subsample': 0.8,  # 子样本比例
        'colsample_bytree': 0.8,  # 特征子采样比例
        'verbose': -1  # 减少输出
    }

    print("🤖 开始训练LightGBM模型...")

    # 模型训练
    model = lgb.train(
        params,
        train_data,
        num_boost_round=1000,  # 最大树数量
        valid_sets=[train_data, test_data],  # 验证集
        valid_names=['train', 'test'],
        callbacks=[lgb.early_stopping(50), lgb.log_evaluation(50)]  # 早停法和日志输出
    )

    # 预测
    print("📊 进行预测...")
    predictions = model.predict(X_test)

    # 评估指标计算
    mse = mean_squared_error(y_test, predictions)
    mae = mean_absolute_error(y_test, predictions)
    rmse = mse ** 0.5

    print(f"\n📈 模型评估结果:")
    print(f"  • MSE: {mse:.6f}")
    print(f"  • RMSE: {rmse:.6f}")
    print(f"  • MAE: {mae:.6f}")

    # 计算方向准确率
    actual_direction = (y_test > 0).astype(int)
    pred_direction = (predictions > 0).astype(int)
    direction_accuracy = (actual_direction == pred_direction).mean()
    print(f"  • 方向准确率: {direction_accuracy:.2%}")

    # 特征重要性分析
    print("\n🔍 特征重要性分析:")
    importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': model.feature_importance()
    }).sort_values('importance', ascending=False)

    print(importance_df.head(10))

    # 保存每日预测结果
    print("\n💾 保存每日预测结果...")
    save_daily_predictions(model, X_test, y_test, df.iloc[split_idx:], feature_cols)

    # 回测策略并生成资金曲线
    print("\n💰 开始策略回测...")
    backtest_results = run_backtest_strategy(model, X_test, y_test, df.iloc[split_idx:])

    # 绘制资金曲线
    plot_equity_curve(backtest_results)

    # 示例预测（未来第2天的预测值）
    last_data = X.iloc[[-1]]  # 使用最后一个数据点
    next_pred = model.predict(last_data)[0]
    print(f"\n🔮 预测未来第2天的收益率: {next_pred:.4%}")

    if next_pred > 0.01:
        print("📈 预测：强烈看涨")
    elif next_pred > 0.005:
        print("📈 预测：看涨")
    elif next_pred > -0.005:
        print("📊 预测：震荡")
    elif next_pred > -0.01:
        print("📉 预测：看跌")
    else:
        print("📉 预测：强烈看跌")

    print("\n✅ 分析完成！")

if __name__ == "__main__":
    main()