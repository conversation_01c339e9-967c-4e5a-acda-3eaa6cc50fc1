# -*- coding: utf-8 -*-
"""
获取币安官方507个USDT永续合约（所有状态）的最后日期
包括可交易、不可交易、已下线等所有状态的合约
使用成功的连接方法，确保获取官方数据
"""

import requests
import json
import csv
import time
from datetime import datetime
import ssl
import urllib3
import os
import socket

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class AllPerpetualLastDatesFetcher:
    """所有永续合约最后日期获取器"""
    
    def __init__(self):
        """初始化获取器"""
        self.session = self.create_optimized_session()
        self.data_dir = "data"
        self.ensure_data_dir()
        
    def ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def create_optimized_session(self):
        """创建优化的HTTP会话 - 使用成功的连接方法"""
        session = requests.Session()
        
        # 关键请求头设置 - 基于成功经验
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache'
        })
        
        # 重试策略 - 基于成功经验
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=2,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def load_all_perpetual_contracts(self):
        """加载完整永续合约列表"""
        contracts_file = os.path.join(self.data_dir, 'all_usdt_perpetual_contracts_simple.json')
        
        if not os.path.exists(contracts_file):
            print("❌ 完整永续合约文件不存在，请先运行 get_all_perpetual_contracts.py")
            return []
        
        try:
            with open(contracts_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            symbols = data.get('symbols', [])
            print(f"✅ 成功加载 {len(symbols)} 个完整永续合约（包括所有状态）")
            return symbols
            
        except Exception as e:
            print(f"❌ 加载完整永续合约文件失败: {str(e)}")
            return []
    
    def get_contract_last_date(self, symbol):
        """获取单个永续合约的最后可用日期"""
        try:
            # 使用币安期货K线API获取最新数据
            url = "https://fapi.binance.com/fapi/v1/klines"
            params = {
                'symbol': symbol,
                'interval': '1d',
                'limit': 1  # 只获取最新的1条数据
            }
            
            response = self.session.get(url, params=params, timeout=60, verify=True)
            
            if response.status_code == 200:
                data = response.json()
                if data and len(data) > 0:
                    # 获取最新数据的时间戳
                    latest_timestamp = data[0][0]  # 开盘时间戳（毫秒）
                    
                    # 转换为日期
                    latest_date = datetime.fromtimestamp(latest_timestamp / 1000)
                    date_str = latest_date.strftime('%Y-%m-%d')
                    
                    # 计算距今天数
                    today = datetime.now()
                    days_ago = (today - latest_date).days
                    
                    # 获取价格信息
                    open_price = float(data[0][1])
                    high_price = float(data[0][2])
                    low_price = float(data[0][3])
                    close_price = float(data[0][4])
                    volume = float(data[0][5])
                    quote_volume = float(data[0][7])  # 成交额
                    
                    return {
                        'symbol': symbol,
                        'last_date': date_str,
                        'last_timestamp': latest_timestamp,
                        'status': 'SUCCESS',
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price,
                        'volume': volume,
                        'quote_volume': quote_volume,
                        'days_ago': days_ago,
                        'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
                        'contract_status': 'ACTIVE'  # 有数据说明合约活跃
                    }
                else:
                    return {
                        'symbol': symbol,
                        'last_date': None,
                        'status': 'NO_DATA',
                        'error': '无数据返回',
                        'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
                        'contract_status': 'INACTIVE'  # 无数据可能是非活跃合约
                    }
            elif response.status_code == 400:
                # 400错误通常表示合约不存在或已下线
                return {
                    'symbol': symbol,
                    'last_date': None,
                    'status': 'CONTRACT_NOT_FOUND',
                    'error': '合约不存在或已下线',
                    'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
                    'contract_status': 'DELISTED'  # 已下线
                }
            else:
                return {
                    'symbol': symbol,
                    'last_date': None,
                    'status': 'API_ERROR',
                    'error': f'HTTP {response.status_code}',
                    'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
                    'contract_status': 'UNKNOWN'
                }
                
        except requests.exceptions.Timeout:
            return {
                'symbol': symbol,
                'last_date': None,
                'status': 'TIMEOUT',
                'error': '请求超时',
                'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
                'contract_status': 'UNKNOWN'
            }
        except Exception as e:
            return {
                'symbol': symbol,
                'last_date': None,
                'status': 'ERROR',
                'error': str(e),
                'data_source': 'OFFICIAL_BINANCE_FUTURES_API',
                'contract_status': 'UNKNOWN'
            }
    
    def fetch_all_last_dates(self, symbols):
        """获取所有永续合约的最后日期"""
        print(f"🚀 开始获取 {len(symbols)} 个完整USDT永续合约的最后可用日期...")
        print("包括可交易、不可交易、已下线等所有状态的合约")
        print("=" * 80)
        
        results = []
        start_time = datetime.now()
        
        for i, symbol in enumerate(symbols, 1):
            print(f"📡 [{i:3d}/{len(symbols)}] 正在获取 {symbol}...")
            
            result = self.get_contract_last_date(symbol)
            results.append(result)
            
            # 显示结果
            if result['status'] == 'SUCCESS':
                days_ago = result.get('days_ago', 0)
                price = result.get('close', 0)
                contract_status = result.get('contract_status', 'UNKNOWN')
                print(f"✅ [{i:3d}] {symbol:20s}: {result['last_date']} ({days_ago}天前, 价格: ${price:,.4f}, 状态: {contract_status})")
            elif result['status'] == 'CONTRACT_NOT_FOUND':
                contract_status = result.get('contract_status', 'UNKNOWN')
                print(f"⚠️  [{i:3d}] {symbol:20s}: {result['status']} - {result.get('error', '')} (状态: {contract_status})")
            else:
                contract_status = result.get('contract_status', 'UNKNOWN')
                print(f"❌ [{i:3d}] {symbol:20s}: {result['status']} - {result.get('error', '')} (状态: {contract_status})")
            
            # 速率控制 - 避免被限制
            time.sleep(0.1)
            
            # 每50个显示一次进度
            if i % 50 == 0:
                elapsed = (datetime.now() - start_time).total_seconds()
                avg_time = elapsed / i
                remaining = (len(symbols) - i) * avg_time
                success_count = len([r for r in results if r['status'] == 'SUCCESS'])
                not_found_count = len([r for r in results if r['status'] == 'CONTRACT_NOT_FOUND'])
                print(f"📊 进度: {i}/{len(symbols)} ({i/len(symbols)*100:.1f}%), 成功: {success_count}, 已下线: {not_found_count}, 预计剩余: {remaining/60:.1f}分钟")
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print("=" * 80)
        print(f"⏱️  总耗时: {duration/60:.1f} 分钟")
        print(f"📊 处理速度: {len(symbols)/duration:.2f} 个/秒")
        
        return results
    
    def analyze_results(self, results):
        """分析结果"""
        print(f"\n📊 结果分析:")
        
        success_count = len([r for r in results if r['status'] == 'SUCCESS'])
        not_found_count = len([r for r in results if r['status'] == 'CONTRACT_NOT_FOUND'])
        no_data_count = len([r for r in results if r['status'] == 'NO_DATA'])
        error_count = len([r for r in results if r['status'] in ['API_ERROR', 'TIMEOUT', 'ERROR']])
        
        print(f"  • 总数: {len(results)}")
        print(f"  • 成功获取数据: {success_count} ({success_count/len(results)*100:.1f}%)")
        print(f"  • 合约已下线: {not_found_count} ({not_found_count/len(results)*100:.1f}%)")
        print(f"  • 无数据: {no_data_count} ({no_data_count/len(results)*100:.1f}%)")
        print(f"  • 错误: {error_count} ({error_count/len(results)*100:.1f}%)")
        
        # 按合约状态分类
        contract_status_stats = {}
        for result in results:
            status = result.get('contract_status', 'UNKNOWN')
            contract_status_stats[status] = contract_status_stats.get(status, 0) + 1
        
        print(f"\n📈 按合约状态分类:")
        for status, count in sorted(contract_status_stats.items()):
            print(f"  • {status}: {count} 个")
        
        if success_count > 0:
            # 分析最后日期分布
            successful_results = [r for r in results if r['status'] == 'SUCCESS']
            days_ago_list = [r['days_ago'] for r in successful_results]
            
            print(f"\n📅 活跃合约数据时效性分析:")
            print(f"  • 最新数据: {min(days_ago_list)} 天前")
            print(f"  • 最旧数据: {max(days_ago_list)} 天前")
            print(f"  • 平均: {sum(days_ago_list)/len(days_ago_list):.1f} 天前")
            
            # 统计不同时效性的数量
            today_count = len([d for d in days_ago_list if d == 0])
            yesterday_count = len([d for d in days_ago_list if d == 1])
            recent_count = len([d for d in days_ago_list if d <= 3])
            old_count = len([d for d in days_ago_list if d > 7])
            
            print(f"\n📊 活跃合约数据分布:")
            print(f"  • 今日数据: {today_count} 个")
            print(f"  • 昨日数据: {yesterday_count} 个")
            print(f"  • 3天内数据: {recent_count} 个")
            print(f"  • 7天前数据: {old_count} 个")
            
            # 价格分析
            prices = [r['close'] for r in successful_results if r.get('close', 0) > 0]
            if prices:
                print(f"\n💰 活跃合约价格范围分析:")
                print(f"  • 最高价格: ${max(prices):,.4f}")
                print(f"  • 最低价格: ${min(prices):,.8f}")
                print(f"  • 平均价格: ${sum(prices)/len(prices):,.4f}")
        
        # 分析已下线合约
        delisted_results = [r for r in results if r['status'] == 'CONTRACT_NOT_FOUND']
        if delisted_results:
            print(f"\n🚫 已下线合约分析:")
            print(f"  • 已下线合约数量: {len(delisted_results)} 个")
            print(f"  • 已下线合约示例:")
            for i, result in enumerate(delisted_results[:10]):
                print(f"    {i+1:2d}. {result['symbol']}")
            if len(delisted_results) > 10:
                print(f"    ... 还有 {len(delisted_results) - 10} 个")
    
    def save_results(self, results):
        """保存结果到文件"""
        print(f"\n💾 保存结果到文件...")
        
        # 保存JSON格式
        json_data = {
            'fetch_time': datetime.now().isoformat(),
            'total_symbols': len(results),
            'successful_count': len([r for r in results if r['status'] == 'SUCCESS']),
            'delisted_count': len([r for r in results if r['status'] == 'CONTRACT_NOT_FOUND']),
            'data_source': '币安官方期货API',
            'api_endpoint': 'https://fapi.binance.com/fapi/v1/klines',
            'official_data': True,
            'data_authenticity': 'OFFICIAL_BINANCE_FUTURES_API',
            'include_all_status': True,
            'note': '包含所有状态的USDT永续合约最后日期：活跃、已下线、无数据等',
            'results': results,
            'statistics': {
                'total_contracts': len(results),
                'active_contracts': len([r for r in results if r['status'] == 'SUCCESS']),
                'delisted_contracts': len([r for r in results if r['status'] == 'CONTRACT_NOT_FOUND']),
                'no_data_contracts': len([r for r in results if r['status'] == 'NO_DATA']),
                'error_contracts': len([r for r in results if r['status'] in ['API_ERROR', 'TIMEOUT', 'ERROR']])
            }
        }
        
        json_file = os.path.join(self.data_dir, 'all_perpetual_contracts_last_dates.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        # 保存CSV格式
        csv_file = os.path.join(self.data_dir, 'all_perpetual_contracts_last_dates.csv')
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow([
                '合约名称', '最后日期', '状态', '合约状态', '天数差', 
                '开盘价', '最高价', '最低价', '收盘价', '成交量', '成交额',
                '数据源', '错误信息'
            ])
            
            # 写入数据
            for result in results:
                writer.writerow([
                    result['symbol'],
                    result.get('last_date', ''),
                    result['status'],
                    result.get('contract_status', ''),
                    result.get('days_ago', ''),
                    result.get('open', ''),
                    result.get('high', ''),
                    result.get('low', ''),
                    result.get('close', ''),
                    result.get('volume', ''),
                    result.get('quote_volume', ''),
                    result.get('data_source', ''),
                    result.get('error', '')
                ])
        
        print(f"✅ 结果已保存:")
        print(f"  • JSON格式: {json_file}")
        print(f"  • CSV格式: {csv_file}")
        
        return json_file, csv_file
    
    def run(self):
        """执行获取流程"""
        print("🚀 开始获取507个完整USDT永续合约的最后可用日期...")
        print("=" * 80)
        
        # 1. 加载完整永续合约列表
        symbols = self.load_all_perpetual_contracts()
        if not symbols:
            print("❌ 无法加载完整永续合约列表，程序退出")
            return False
        
        # 2. 获取最后日期
        results = self.fetch_all_last_dates(symbols)
        
        # 3. 分析结果
        self.analyze_results(results)
        
        # 4. 保存结果
        self.save_results(results)
        
        print(f"\n🎉 获取完成！")
        print(f"📊 共处理 {len(symbols)} 个完整USDT永续合约")
        print(f"📁 文件保存在 {self.data_dir} 目录下")
        print(f"✅ 数据来源: 100% 币安官方期货API")
        
        return True

def main():
    """主函数"""
    try:
        fetcher = AllPerpetualLastDatesFetcher()
        success = fetcher.run()
        
        if success:
            print("\n✅ 程序执行成功")
        else:
            print("\n❌ 程序执行失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
    
    print("\n程序结束")

if __name__ == "__main__":
    main()
