# -*- coding: utf-8 -*-
"""
简单展示BTC-ETH策略的资金曲线
"""

import pandas as pd
import matplotlib.pyplot as plt
import json
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def display_btc_eth_strategy():
    """展示BTC-ETH策略结果"""
    
    print("📊 展示BTC-ETH选优策略资金曲线")
    print("=" * 60)
    
    # 检查文件是否存在
    results_dir = "data/btc_eth_strategy_results"
    detailed_file = os.path.join(results_dir, "detailed_results.csv")
    performance_file = os.path.join(results_dir, "performance_metrics.json")
    
    if not os.path.exists(detailed_file) or not os.path.exists(performance_file):
        print("❌ 未找到BTC-ETH策略回测结果")
        return
    
    # 加载数据
    print("📈 加载回测数据...")
    results_df = pd.read_csv(detailed_file)
    results_df['date'] = pd.to_datetime(results_df['date'])
    
    with open(performance_file, 'r', encoding='utf-8') as f:
        performance = json.load(f)
    
    # 显示策略概况
    print(f"\n📊 策略概况:")
    print(f"  • 回测期间: {performance['years']:.1f} 年")
    print(f"  • 交易天数: {performance['total_trades']:,} 天")
    print(f"  • 策略总收益: {performance['strategy_total_return']:.2%}")
    print(f"  • 基准总收益: {performance['benchmark_total_return']:.2%}")
    print(f"  • 超额收益: {performance['excess_return']:.2%}")
    print(f"  • 年化收益率: {performance['strategy_annual_return']:.2%}")
    print(f"  • 最大回撤: {performance['max_drawdown']:.2%}")
    print(f"  • 夏普比率: {performance['strategy_sharpe']:.3f}")
    print(f"  • 最终资金: {performance['final_capital']:,.0f} USDT")
    
    # 创建资金曲线图
    print(f"\n📈 生成资金曲线图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('BTC-ETH选优策略回测结果', fontsize=16, fontweight='bold')
    
    # 1. 资金曲线对比
    ax1 = axes[0, 0]
    ax1.plot(results_df['date'], results_df['capital'], label='选优策略', linewidth=2, color='blue')
    ax1.plot(results_df['date'], results_df['benchmark_capital'], label='等权重基准', linewidth=2, color='red', alpha=0.7)
    ax1.set_title('资金曲线对比')
    ax1.set_xlabel('日期')
    ax1.set_ylabel('资金 (USDT)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加收益率标注
    strategy_return = performance['strategy_total_return']
    benchmark_return = performance['benchmark_total_return']
    ax1.text(0.02, 0.98, f'策略收益: {strategy_return:.2%}', transform=ax1.transAxes, 
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    ax1.text(0.02, 0.90, f'基准收益: {benchmark_return:.2%}', transform=ax1.transAxes, 
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
    
    # 格式化日期轴
    if len(results_df) > 100:
        ax1.xaxis.set_major_locator(plt.MaxNLocator(8))
    
    # 2. 回撤分析
    ax2 = axes[0, 1]
    ax2.fill_between(results_df['date'], results_df['strategy_drawdown'], 0, 
                    alpha=0.3, color='red', label='策略回撤')
    ax2.fill_between(results_df['date'], results_df['benchmark_drawdown'], 0, 
                    alpha=0.3, color='orange', label='基准回撤')
    ax2.set_title('回撤分析')
    ax2.set_xlabel('日期')
    ax2.set_ylabel('回撤比例')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    if len(results_df) > 100:
        ax2.xaxis.set_major_locator(plt.MaxNLocator(8))
    
    # 3. 持仓分布
    ax3 = axes[1, 0]
    position_stats = performance['position_stats']
    labels = list(position_stats.keys())
    sizes = list(position_stats.values())
    colors = ['#1f77b4', '#ff7f0e']
    
    wedges, texts, autotexts = ax3.pie(sizes, labels=labels, autopct='%1.1f%%', 
                                      colors=colors, startangle=90)
    ax3.set_title('持仓分布')
    
    # 4. 月度收益分布
    ax4 = axes[1, 1]
    
    # 计算月度收益
    results_df['year_month'] = results_df['date'].dt.to_period('M')
    monthly_returns = []
    
    for month, group in results_df.groupby('year_month'):
        if len(group) > 1:
            month_return = (group['capital'].iloc[-1] / group['capital'].iloc[0] - 1) * 100
            monthly_returns.append(month_return)
    
    if monthly_returns:
        ax4.hist(monthly_returns, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax4.set_title('月度收益分布')
        ax4.set_xlabel('月收益率 (%)')
        ax4.set_ylabel('频次')
        ax4.grid(True, alpha=0.3)
        
        # 添加统计信息
        mean_return = sum(monthly_returns) / len(monthly_returns)
        ax4.axvline(mean_return, color='red', linestyle='--', linewidth=2, label=f'平均: {mean_return:.1f}%')
        ax4.legend()
    
    plt.tight_layout()
    
    # 保存图表
    chart_file = "btc_eth_equity_curve.png"
    plt.savefig(chart_file, dpi=300, bbox_inches='tight')
    print(f"📊 资金曲线图已保存: {chart_file}")
    
    plt.show()
    
    # 显示关键统计
    print(f"\n📊 关键统计数据:")
    print(f"  • 起始日期: {results_df['date'].min().strftime('%Y-%m-%d')}")
    print(f"  • 结束日期: {results_df['date'].max().strftime('%Y-%m-%d')}")
    print(f"  • 最高资金: {results_df['capital'].max():,.0f} USDT")
    print(f"  • 最低资金: {results_df['capital'].min():,.0f} USDT")
    
    # 计算年度表现
    print(f"\n📅 年度表现:")
    results_df['year'] = results_df['date'].dt.year
    yearly_performance = []
    
    for year, group in results_df.groupby('year'):
        if len(group) > 1:
            year_return = (group['capital'].iloc[-1] / group['capital'].iloc[0] - 1) * 100
            yearly_performance.append((year, year_return))
    
    for year, return_pct in yearly_performance:
        print(f"  • {year}年: {return_pct:+6.1f}%")
    
    # 风险指标
    print(f"\n⚠️ 风险指标:")
    print(f"  • 最大回撤: {performance['max_drawdown']:.2%}")
    print(f"  • 夏普比率: {performance['strategy_sharpe']:.3f}")
    print(f"  • 胜率: {performance['win_rate']:.2%}")
    print(f"  • 波动率: {results_df['strategy_returns'].std() * (252**0.5):.2%} (年化)")
    
    return True

def main():
    """主函数"""
    success = display_btc_eth_strategy()
    
    if success:
        print(f"\n✅ 资金曲线展示完成！")
    else:
        print(f"\n❌ 资金曲线展示失败")

if __name__ == "__main__":
    main()
