# -*- coding: utf-8 -*-
"""
使用LightGBM预测比特币和以太坊涨跌幅的选优交易策略回测
包含资金曲线图展示
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
import os
import json
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error
import seaborn as sns

# 设置中文字体和忽略警告
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

class LightGBMCryptoStrategy:
    """LightGBM加密货币预测策略"""
    
    def __init__(self):
        """初始化策略"""
        self.data_dir = "data/btc_eth_data"
        self.results_dir = "data/lightgbm_results"
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 策略参数
        self.initial_capital = 100000  # 初始资金10万USDT
        self.transaction_cost = 0.001  # 交易费用0.1%
        self.lookback_days = 30  # 特征回看天数
        self.prediction_days = 1  # 预测未来1天
        
        # LightGBM参数
        self.lgb_params = {
            'objective': 'regression',
            'metric': 'rmse',
            'boosting_type': 'gbdt',
            'num_leaves': 31,
            'learning_rate': 0.05,
            'feature_fraction': 0.9,
            'bagging_fraction': 0.8,
            'bagging_freq': 5,
            'verbose': -1,
            'random_state': 42
        }
        
    def load_data(self):
        """加载BTC和ETH数据"""
        print("📊 加载比特币和以太坊历史数据...")
        
        # 加载合并数据
        combined_file = os.path.join(self.data_dir, "btc_eth_combined.csv")
        df = pd.read_csv(combined_file)
        df['date'] = pd.to_datetime(df['date'])
        
        print(f"✅ 数据加载完成:")
        print(f"  • 总记录数: {len(df):,} 条")
        print(f"  • 日期范围: {df['date'].min()} 到 {df['date'].max()}")
        print(f"  • BTC记录: {len(df[df['symbol']=='BTCUSDT']):,} 条")
        print(f"  • ETH记录: {len(df[df['symbol']=='ETHUSDT']):,} 条")
        
        return df
    
    def create_features(self, df):
        """创建技术指标特征"""
        print("🔧 创建技术指标特征...")
        
        features_df = []
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            symbol_df = df[df['symbol'] == symbol].copy().sort_values('date').reset_index(drop=True)
            
            # 基础价格特征
            symbol_df['returns'] = symbol_df['close'].pct_change()
            symbol_df['log_returns'] = np.log(symbol_df['close'] / symbol_df['close'].shift(1))
            symbol_df['high_low_ratio'] = symbol_df['high'] / symbol_df['low']
            symbol_df['volume_ratio'] = symbol_df['volume'] / symbol_df['volume'].rolling(20).mean()
            
            # 移动平均线
            for period in [5, 10, 20, 50]:
                symbol_df[f'ma_{period}'] = symbol_df['close'].rolling(period).mean()
                symbol_df[f'ma_ratio_{period}'] = symbol_df['close'] / symbol_df[f'ma_{period}']
            
            # 技术指标
            # RSI
            delta = symbol_df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            symbol_df['rsi'] = 100 - (100 / (1 + rs))
            
            # 布林带
            symbol_df['bb_middle'] = symbol_df['close'].rolling(20).mean()
            bb_std = symbol_df['close'].rolling(20).std()
            symbol_df['bb_upper'] = symbol_df['bb_middle'] + (bb_std * 2)
            symbol_df['bb_lower'] = symbol_df['bb_middle'] - (bb_std * 2)
            symbol_df['bb_position'] = (symbol_df['close'] - symbol_df['bb_lower']) / (symbol_df['bb_upper'] - symbol_df['bb_lower'])
            
            # MACD
            exp1 = symbol_df['close'].ewm(span=12).mean()
            exp2 = symbol_df['close'].ewm(span=26).mean()
            symbol_df['macd'] = exp1 - exp2
            symbol_df['macd_signal'] = symbol_df['macd'].ewm(span=9).mean()
            symbol_df['macd_histogram'] = symbol_df['macd'] - symbol_df['macd_signal']
            
            # 波动率
            symbol_df['volatility'] = symbol_df['returns'].rolling(20).std()
            
            # 价格位置
            symbol_df['price_position'] = (symbol_df['close'] - symbol_df['close'].rolling(20).min()) / \
                                        (symbol_df['close'].rolling(20).max() - symbol_df['close'].rolling(20).min())
            
            # 滞后特征
            for lag in [1, 2, 3, 5, 10]:
                symbol_df[f'returns_lag_{lag}'] = symbol_df['returns'].shift(lag)
                symbol_df[f'volume_lag_{lag}'] = symbol_df['volume'].shift(lag)
            
            # 目标变量：未来1天收益率
            symbol_df['target'] = symbol_df['returns'].shift(-self.prediction_days)
            
            features_df.append(symbol_df)
        
        # 合并数据
        combined_features = pd.concat(features_df, ignore_index=True)
        combined_features = combined_features.sort_values(['symbol', 'date']).reset_index(drop=True)
        
        print(f"✅ 特征创建完成，共 {combined_features.shape[1]} 个特征")
        
        return combined_features
    
    def prepare_training_data(self, df):
        """准备训练数据"""
        print("📋 准备训练数据...")
        
        # 选择特征列
        feature_cols = [col for col in df.columns if col not in 
                       ['date', 'symbol', 'timestamp', 'close_time', 'target'] and 
                       not col.startswith('ma_') and not col.startswith('bb_')]
        
        # 添加重要的技术指标
        important_features = ['ma_ratio_5', 'ma_ratio_10', 'ma_ratio_20', 'rsi', 'bb_position', 
                            'macd', 'macd_signal', 'volatility', 'price_position']
        feature_cols.extend([col for col in important_features if col in df.columns])
        
        # 去除重复
        feature_cols = list(set(feature_cols))
        
        print(f"📊 选择的特征数量: {len(feature_cols)}")
        
        # 删除包含NaN的行
        df_clean = df[['date', 'symbol', 'close', 'target'] + feature_cols].dropna()
        
        print(f"📊 清理后数据量: {len(df_clean)} 条")
        
        return df_clean, feature_cols
    
    def train_models(self, df, feature_cols):
        """训练LightGBM模型"""
        print("🤖 训练LightGBM模型...")
        
        models = {}
        predictions = {}
        
        for symbol in ['BTCUSDT', 'ETHUSDT']:
            print(f"📈 训练 {symbol} 模型...")
            
            symbol_df = df[df['symbol'] == symbol].copy().sort_values('date').reset_index(drop=True)
            
            # 准备训练数据
            X = symbol_df[feature_cols].values
            y = symbol_df['target'].values
            dates = symbol_df['date'].values
            
            # 时间序列分割
            tscv = TimeSeriesSplit(n_splits=5)
            
            model_predictions = []
            model_scores = []
            
            for train_idx, val_idx in tscv.split(X):
                X_train, X_val = X[train_idx], X[val_idx]
                y_train, y_val = y[train_idx], y[val_idx]
                
                # 训练模型
                train_data = lgb.Dataset(X_train, label=y_train)
                val_data = lgb.Dataset(X_val, label=y_val, reference=train_data)
                
                model = lgb.train(
                    self.lgb_params,
                    train_data,
                    valid_sets=[val_data],
                    num_boost_round=1000,
                    callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)]
                )
                
                # 预测
                val_pred = model.predict(X_val)
                model_predictions.extend(list(zip(val_idx, val_pred)))
                
                # 评估
                mse = mean_squared_error(y_val, val_pred)
                mae = mean_absolute_error(y_val, val_pred)
                model_scores.append({'mse': mse, 'mae': mae})
            
            # 训练最终模型（使用所有数据）
            train_data = lgb.Dataset(X, label=y)
            final_model = lgb.train(
                self.lgb_params,
                train_data,
                num_boost_round=500,
                callbacks=[lgb.log_evaluation(0)]
            )
            
            models[symbol] = final_model
            
            # 整理预测结果
            pred_df = pd.DataFrame(model_predictions, columns=['idx', 'prediction'])
            pred_df = pred_df.sort_values('idx')
            
            symbol_df_pred = symbol_df.iloc[pred_df['idx']].copy()
            symbol_df_pred['prediction'] = pred_df['prediction'].values
            
            predictions[symbol] = symbol_df_pred
            
            # 显示模型性能
            avg_mse = np.mean([s['mse'] for s in model_scores])
            avg_mae = np.mean([s['mae'] for s in model_scores])
            print(f"  • {symbol} 模型性能: MSE={avg_mse:.6f}, MAE={avg_mae:.6f}")
        
        return models, predictions
    
    def backtest_strategy(self, predictions):
        """回测策略"""
        print("📊 执行策略回测...")
        
        # 合并预测数据
        all_predictions = []
        for symbol, pred_df in predictions.items():
            pred_df = pred_df.copy()
            pred_df['symbol'] = symbol
            all_predictions.append(pred_df)
        
        combined_pred = pd.concat(all_predictions, ignore_index=True)
        combined_pred = combined_pred.sort_values('date').reset_index(drop=True)
        
        # 策略逻辑：选择预测收益率最高的币种
        strategy_results = []
        capital = self.initial_capital
        position = None
        
        # 按日期分组
        for date, day_data in combined_pred.groupby('date'):
            if len(day_data) < 2:  # 需要两个币种的数据
                continue
            
            # 选择预测收益率最高的币种
            best_coin = day_data.loc[day_data['prediction'].idxmax()]
            
            # 如果预测收益率为正，则买入；否则空仓
            if best_coin['prediction'] > 0.001:  # 阈值：0.1%
                new_position = best_coin['symbol']
            else:
                new_position = None
            
            # 计算收益
            if position is not None:
                # 获取当前持仓币种的实际收益
                current_coin_data = day_data[day_data['symbol'] == position]
                if len(current_coin_data) > 0:
                    actual_return = current_coin_data.iloc[0]['target']
                    if not pd.isna(actual_return):
                        # 扣除交易费用
                        net_return = actual_return - self.transaction_cost
                        capital *= (1 + net_return)
            
            # 更新持仓
            if new_position != position:
                # 换仓需要额外扣除交易费用
                if position is not None and new_position is not None:
                    capital *= (1 - self.transaction_cost)
            
            position = new_position
            
            strategy_results.append({
                'date': date,
                'capital': capital,
                'position': position,
                'best_prediction': best_coin['prediction'],
                'best_symbol': best_coin['symbol']
            })
        
        strategy_df = pd.DataFrame(strategy_results)
        
        # 计算基准收益（等权重买入持有）
        benchmark_results = []
        benchmark_capital = self.initial_capital
        
        for date, day_data in combined_pred.groupby('date'):
            if len(day_data) < 2:
                continue
            
            # 等权重收益
            avg_return = day_data['target'].mean()
            if not pd.isna(avg_return):
                benchmark_capital *= (1 + avg_return - self.transaction_cost)
            
            benchmark_results.append({
                'date': date,
                'benchmark_capital': benchmark_capital
            })
        
        benchmark_df = pd.DataFrame(benchmark_results)
        
        # 合并结果
        final_results = strategy_df.merge(benchmark_df, on='date', how='inner')
        
        return final_results
    
    def analyze_performance(self, results_df):
        """分析策略表现"""
        print("📈 分析策略表现...")
        
        # 计算收益率
        results_df['strategy_returns'] = results_df['capital'].pct_change()
        results_df['benchmark_returns'] = results_df['benchmark_capital'].pct_change()
        
        # 计算累计收益
        strategy_total_return = (results_df['capital'].iloc[-1] / self.initial_capital) - 1
        benchmark_total_return = (results_df['benchmark_capital'].iloc[-1] / self.initial_capital) - 1
        
        # 计算年化收益率
        days = (results_df['date'].iloc[-1] - results_df['date'].iloc[0]).days
        strategy_annual_return = (1 + strategy_total_return) ** (365 / days) - 1
        benchmark_annual_return = (1 + benchmark_total_return) ** (365 / days) - 1
        
        # 计算最大回撤
        results_df['strategy_peak'] = results_df['capital'].cummax()
        results_df['strategy_drawdown'] = (results_df['capital'] - results_df['strategy_peak']) / results_df['strategy_peak']
        max_drawdown = results_df['strategy_drawdown'].min()
        
        results_df['benchmark_peak'] = results_df['benchmark_capital'].cummax()
        results_df['benchmark_drawdown'] = (results_df['benchmark_capital'] - results_df['benchmark_peak']) / results_df['benchmark_peak']
        benchmark_max_drawdown = results_df['benchmark_drawdown'].min()
        
        # 计算夏普比率
        strategy_sharpe = results_df['strategy_returns'].mean() / results_df['strategy_returns'].std() * np.sqrt(252)
        benchmark_sharpe = results_df['benchmark_returns'].mean() / results_df['benchmark_returns'].std() * np.sqrt(252)
        
        # 计算胜率
        win_rate = (results_df['strategy_returns'] > 0).mean()
        
        performance = {
            'strategy_total_return': strategy_total_return,
            'benchmark_total_return': benchmark_total_return,
            'strategy_annual_return': strategy_annual_return,
            'benchmark_annual_return': benchmark_annual_return,
            'excess_return': strategy_total_return - benchmark_total_return,
            'max_drawdown': max_drawdown,
            'benchmark_max_drawdown': benchmark_max_drawdown,
            'strategy_sharpe': strategy_sharpe,
            'benchmark_sharpe': benchmark_sharpe,
            'win_rate': win_rate,
            'total_trades': len(results_df),
            'final_capital': results_df['capital'].iloc[-1],
            'benchmark_final_capital': results_df['benchmark_capital'].iloc[-1]
        }
        
        return performance, results_df
    
    def plot_results(self, results_df, performance):
        """绘制结果图表"""
        print("📊 生成结果图表...")
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('LightGBM加密货币预测策略回测结果', fontsize=16, fontweight='bold')
        
        # 1. 资金曲线图
        ax1 = axes[0, 0]
        ax1.plot(results_df['date'], results_df['capital'], label='LightGBM策略', linewidth=2, color='blue')
        ax1.plot(results_df['date'], results_df['benchmark_capital'], label='等权重基准', linewidth=2, color='red', alpha=0.7)
        ax1.set_title('资金曲线对比', fontsize=14, fontweight='bold')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('资金 (USDT)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        
        # 添加收益率标注
        final_strategy_return = performance['strategy_total_return']
        final_benchmark_return = performance['benchmark_total_return']
        ax1.text(0.02, 0.98, f'策略收益: {final_strategy_return:.2%}', transform=ax1.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        ax1.text(0.02, 0.90, f'基准收益: {final_benchmark_return:.2%}', transform=ax1.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
        
        # 2. 回撤图
        ax2 = axes[0, 1]
        ax2.fill_between(results_df['date'], results_df['strategy_drawdown'], 0, 
                        alpha=0.3, color='red', label='策略回撤')
        ax2.fill_between(results_df['date'], results_df['benchmark_drawdown'], 0, 
                        alpha=0.3, color='orange', label='基准回撤')
        ax2.set_title('回撤分析', fontsize=14, fontweight='bold')
        ax2.set_xlabel('日期')
        ax2.set_ylabel('回撤比例')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax2.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
        
        # 3. 持仓分布
        ax3 = axes[1, 0]
        position_counts = results_df['position'].value_counts()
        position_counts = position_counts[position_counts.index.notna()]  # 移除空仓
        if len(position_counts) > 0:
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
            ax3.pie(position_counts.values, labels=position_counts.index, autopct='%1.1f%%', 
                   colors=colors[:len(position_counts)])
            ax3.set_title('持仓分布', fontsize=14, fontweight='bold')
        
        # 4. 性能指标表
        ax4 = axes[1, 1]
        ax4.axis('off')
        
        metrics_data = [
            ['指标', '策略', '基准'],
            ['总收益率', f"{performance['strategy_total_return']:.2%}", f"{performance['benchmark_total_return']:.2%}"],
            ['年化收益率', f"{performance['strategy_annual_return']:.2%}", f"{performance['benchmark_annual_return']:.2%}"],
            ['最大回撤', f"{performance['max_drawdown']:.2%}", f"{performance['benchmark_max_drawdown']:.2%}"],
            ['夏普比率', f"{performance['strategy_sharpe']:.3f}", f"{performance['benchmark_sharpe']:.3f}"],
            ['胜率', f"{performance['win_rate']:.2%}", '-'],
            ['超额收益', f"{performance['excess_return']:.2%}", '-'],
            ['最终资金', f"{performance['final_capital']:,.0f}", f"{performance['benchmark_final_capital']:,.0f}"]
        ]
        
        table = ax4.table(cellText=metrics_data[1:], colLabels=metrics_data[0], 
                         cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 2)
        
        # 设置表格样式
        for i in range(len(metrics_data)):
            for j in range(len(metrics_data[0])):
                if i == 0:  # 表头
                    table[(i, j)].set_facecolor('#4CAF50')
                    table[(i, j)].set_text_props(weight='bold', color='white')
                elif j == 0:  # 第一列
                    table[(i, j)].set_facecolor('#E8F5E8')
                    table[(i, j)].set_text_props(weight='bold')
                else:
                    table[(i, j)].set_facecolor('#F5F5F5')
        
        ax4.set_title('策略性能指标', fontsize=14, fontweight='bold', pad=20)
        
        plt.tight_layout()
        
        # 保存图表
        chart_file = os.path.join(self.results_dir, 'lightgbm_strategy_results.png')
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {chart_file}")
        
        plt.show()
        
        return chart_file
    
    def save_results(self, results_df, performance, models):
        """保存结果"""
        print("💾 保存回测结果...")
        
        # 保存详细结果
        results_file = os.path.join(self.results_dir, 'detailed_results.csv')
        results_df.to_csv(results_file, index=False, encoding='utf-8-sig')
        
        # 保存性能指标
        performance_file = os.path.join(self.results_dir, 'performance_metrics.json')
        with open(performance_file, 'w', encoding='utf-8') as f:
            json.dump(performance, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存策略配置
        config = {
            'strategy_name': 'LightGBM_Crypto_Selection',
            'initial_capital': self.initial_capital,
            'transaction_cost': self.transaction_cost,
            'lookback_days': self.lookback_days,
            'prediction_days': self.prediction_days,
            'lgb_params': self.lgb_params,
            'backtest_period': {
                'start_date': results_df['date'].min().isoformat(),
                'end_date': results_df['date'].max().isoformat(),
                'total_days': len(results_df)
            }
        }
        
        config_file = os.path.join(self.results_dir, 'strategy_config.json')
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 结果已保存:")
        print(f"  • 详细结果: {results_file}")
        print(f"  • 性能指标: {performance_file}")
        print(f"  • 策略配置: {config_file}")
    
    def run(self):
        """执行完整的策略回测流程"""
        print("🚀 开始LightGBM加密货币预测策略回测...")
        print("=" * 80)
        
        try:
            # 1. 加载数据
            df = self.load_data()
            
            # 2. 创建特征
            features_df = self.create_features(df)
            
            # 3. 准备训练数据
            clean_df, feature_cols = self.prepare_training_data(features_df)
            
            # 4. 训练模型
            models, predictions = self.train_models(clean_df, feature_cols)
            
            # 5. 回测策略
            results_df = self.backtest_strategy(predictions)
            
            # 6. 分析表现
            performance, results_df = self.analyze_performance(results_df)
            
            # 7. 绘制图表
            chart_file = self.plot_results(results_df, performance)
            
            # 8. 保存结果
            self.save_results(results_df, performance, models)
            
            # 9. 显示总结
            print("\n🎉 LightGBM策略回测完成！")
            print("=" * 80)
            print(f"📈 策略表现总结:")
            print(f"  • 策略总收益率: {performance['strategy_total_return']:.2%}")
            print(f"  • 基准总收益率: {performance['benchmark_total_return']:.2%}")
            print(f"  • 超额收益: {performance['excess_return']:.2%}")
            print(f"  • 年化收益率: {performance['strategy_annual_return']:.2%}")
            print(f"  • 最大回撤: {performance['max_drawdown']:.2%}")
            print(f"  • 夏普比率: {performance['strategy_sharpe']:.3f}")
            print(f"  • 胜率: {performance['win_rate']:.2%}")
            print(f"  • 最终资金: {performance['final_capital']:,.0f} USDT")
            print(f"📁 结果保存在: {self.results_dir}")
            
            return True
            
        except Exception as e:
            print(f"❌ 策略回测失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    strategy = LightGBMCryptoStrategy()
    success = strategy.run()
    
    if success:
        print("\n✅ 程序执行成功")
    else:
        print("\n❌ 程序执行失败")

if __name__ == "__main__":
    main()
