# -*- coding: utf-8 -*-
"""
使用507个USDT永续合约全量历史数据进行最简单的策略回测
策略：双移动平均线交叉策略（MA5 > MA20 做多，MA5 < MA20 做空）
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import csv
import glob
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

class SimpleStrategyBacktest:
    """简单策略回测器"""
    
    def __init__(self):
        """初始化回测器"""
        self.data_dir = "data"
        self.historical_dir = os.path.join(self.data_dir, "perpetual_historical")
        self.results_dir = os.path.join(self.data_dir, "backtest_results")
        self.ensure_directories()
        
        # 策略参数
        self.short_ma = 5   # 短期移动平均线
        self.long_ma = 20   # 长期移动平均线
        self.initial_capital = 10000  # 初始资金（USDT）
        self.position_size = 0.1  # 每次交易的仓位比例
        self.transaction_cost = 0.001  # 交易费用（0.1%）
        
    def ensure_directories(self):
        """确保目录存在"""
        os.makedirs(self.results_dir, exist_ok=True)
    
    def load_contract_data(self, symbol):
        """加载单个合约的历史数据"""
        csv_file = os.path.join(self.historical_dir, f"{symbol}_historical.csv")
        
        if not os.path.exists(csv_file):
            return None
        
        try:
            df = pd.read_csv(csv_file)
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date').reset_index(drop=True)
            
            # 确保有足够的数据进行移动平均计算
            if len(df) < self.long_ma + 10:
                return None
            
            return df
        except Exception as e:
            print(f"❌ 加载 {symbol} 数据失败: {e}")
            return None
    
    def calculate_indicators(self, df):
        """计算技术指标"""
        # 计算移动平均线
        df[f'MA{self.short_ma}'] = df['close'].rolling(window=self.short_ma).mean()
        df[f'MA{self.long_ma}'] = df['close'].rolling(window=self.long_ma).mean()
        
        # 生成交易信号
        df['signal'] = 0
        df.loc[df[f'MA{self.short_ma}'] > df[f'MA{self.long_ma}'], 'signal'] = 1  # 做多信号
        df.loc[df[f'MA{self.short_ma}'] < df[f'MA{self.long_ma}'], 'signal'] = -1  # 做空信号
        
        # 计算信号变化（交易点）
        df['signal_change'] = df['signal'].diff()
        
        return df
    
    def backtest_single_contract(self, symbol, df):
        """对单个合约进行回测"""
        # 计算指标
        df = self.calculate_indicators(df)
        
        # 初始化回测变量
        capital = self.initial_capital
        position = 0  # 当前仓位（正数做多，负数做空）
        trades = []
        equity_curve = []
        
        for i in range(self.long_ma, len(df)):
            current_date = df.iloc[i]['date']
            current_price = df.iloc[i]['close']
            current_signal = df.iloc[i]['signal']
            signal_change = df.iloc[i]['signal_change']
            
            # 检查是否有交易信号
            if signal_change != 0 and not pd.isna(signal_change):
                # 平仓当前持仓
                if position != 0:
                    # 计算平仓收益
                    if position > 0:  # 平多仓
                        pnl = position * (current_price - entry_price)
                    else:  # 平空仓
                        pnl = position * (entry_price - current_price)
                    
                    # 扣除交易费用
                    transaction_fee = abs(position) * current_price * self.transaction_cost
                    pnl -= transaction_fee
                    
                    capital += pnl
                    
                    # 记录交易
                    trades.append({
                        'symbol': symbol,
                        'entry_date': entry_date,
                        'exit_date': current_date,
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'position': position,
                        'pnl': pnl,
                        'capital': capital
                    })
                
                # 开新仓
                if current_signal != 0:
                    position_value = capital * self.position_size
                    position = position_value / current_price * current_signal
                    entry_price = current_price
                    entry_date = current_date
                    
                    # 扣除开仓费用
                    transaction_fee = abs(position) * current_price * self.transaction_cost
                    capital -= transaction_fee
                else:
                    position = 0
            
            # 计算当前权益
            if position != 0:
                if position > 0:  # 多仓
                    unrealized_pnl = position * (current_price - entry_price)
                else:  # 空仓
                    unrealized_pnl = position * (entry_price - current_price)
                current_equity = capital + unrealized_pnl
            else:
                current_equity = capital
            
            equity_curve.append({
                'date': current_date,
                'equity': current_equity,
                'position': position,
                'price': current_price
            })
        
        # 计算策略表现
        if len(equity_curve) > 0:
            equity_df = pd.DataFrame(equity_curve)
            equity_df['returns'] = equity_df['equity'].pct_change()
            
            # 计算基准收益（买入持有）
            start_price = df.iloc[self.long_ma]['close']
            end_price = df.iloc[-1]['close']
            buy_hold_return = (end_price - start_price) / start_price
            
            # 计算策略收益
            strategy_return = (equity_df['equity'].iloc[-1] - self.initial_capital) / self.initial_capital
            
            # 计算其他指标
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t['pnl'] > 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 计算最大回撤
            equity_df['peak'] = equity_df['equity'].cummax()
            equity_df['drawdown'] = (equity_df['equity'] - equity_df['peak']) / equity_df['peak']
            max_drawdown = equity_df['drawdown'].min()
            
            # 计算夏普比率
            if len(equity_df['returns'].dropna()) > 1:
                sharpe_ratio = equity_df['returns'].mean() / equity_df['returns'].std() * np.sqrt(252)
            else:
                sharpe_ratio = 0
            
            return {
                'symbol': symbol,
                'start_date': df.iloc[self.long_ma]['date'].strftime('%Y-%m-%d'),
                'end_date': df.iloc[-1]['date'].strftime('%Y-%m-%d'),
                'total_days': len(equity_curve),
                'strategy_return': strategy_return,
                'buy_hold_return': buy_hold_return,
                'excess_return': strategy_return - buy_hold_return,
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'win_rate': win_rate,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'final_capital': equity_df['equity'].iloc[-1],
                'trades': trades,
                'equity_curve': equity_curve
            }
        else:
            return None
    
    def run_backtest_for_contract(self, symbol):
        """运行单个合约的回测"""
        print(f"📊 回测 {symbol}...")
        
        # 加载数据
        df = self.load_contract_data(symbol)
        if df is None:
            return None
        
        # 执行回测
        result = self.backtest_single_contract(symbol, df)
        return result
    
    def run_all_backtests(self):
        """运行所有合约的回测"""
        print("🚀 开始对507个USDT永续合约进行简单策略回测...")
        print(f"📈 策略: MA{self.short_ma} vs MA{self.long_ma} 双移动平均线交叉")
        print("=" * 80)
        
        # 获取所有历史数据文件
        csv_files = glob.glob(os.path.join(self.historical_dir, "*_historical.csv"))
        symbols = [os.path.basename(f).replace('_historical.csv', '') for f in csv_files]
        
        print(f"📁 找到 {len(symbols)} 个合约的历史数据文件")
        
        results = []
        successful_backtests = 0
        failed_backtests = 0
        
        # 顺序处理每个合约（避免内存问题）
        for i, symbol in enumerate(symbols, 1):
            try:
                result = self.run_backtest_for_contract(symbol)
                if result:
                    results.append(result)
                    successful_backtests += 1
                    print(f"✅ [{i:3d}/{len(symbols)}] {symbol:20s}: 收益率 {result['strategy_return']:8.2%}, 交易次数 {result['total_trades']:3d}, 胜率 {result['win_rate']:6.1%}")
                else:
                    failed_backtests += 1
                    print(f"❌ [{i:3d}/{len(symbols)}] {symbol:20s}: 回测失败")
            except Exception as e:
                failed_backtests += 1
                print(f"❌ [{i:3d}/{len(symbols)}] {symbol:20s}: 异常 - {str(e)}")
            
            # 每50个显示进度
            if i % 50 == 0:
                print(f"📊 进度: {i}/{len(symbols)} ({i/len(symbols)*100:.1f}%), 成功: {successful_backtests}, 失败: {failed_backtests}")
        
        print("=" * 80)
        print(f"📊 回测完成统计:")
        print(f"  • 总合约数: {len(symbols)}")
        print(f"  • 成功回测: {successful_backtests}")
        print(f"  • 失败回测: {failed_backtests}")
        print(f"  • 成功率: {successful_backtests/len(symbols)*100:.1f}%")
        
        return results
    
    def analyze_results(self, results):
        """分析回测结果"""
        if not results:
            print("❌ 没有有效的回测结果")
            return
        
        print(f"\n📊 策略表现分析 (基于 {len(results)} 个合约):")
        
        # 转换为DataFrame便于分析
        df = pd.DataFrame([{
            'symbol': r['symbol'],
            'strategy_return': r['strategy_return'],
            'buy_hold_return': r['buy_hold_return'],
            'excess_return': r['excess_return'],
            'total_trades': r['total_trades'],
            'win_rate': r['win_rate'],
            'max_drawdown': r['max_drawdown'],
            'sharpe_ratio': r['sharpe_ratio'],
            'total_days': r['total_days']
        } for r in results])
        
        # 基本统计
        print(f"\n📈 收益率统计:")
        print(f"  • 策略平均收益率: {df['strategy_return'].mean():8.2%}")
        print(f"  • 策略收益率中位数: {df['strategy_return'].median():8.2%}")
        print(f"  • 买入持有平均收益率: {df['buy_hold_return'].mean():8.2%}")
        print(f"  • 平均超额收益: {df['excess_return'].mean():8.2%}")
        
        # 胜率统计
        positive_returns = len(df[df['strategy_return'] > 0])
        print(f"\n🎯 胜率统计:")
        print(f"  • 盈利合约数: {positive_returns} / {len(df)} ({positive_returns/len(df)*100:.1f}%)")
        print(f"  • 跑赢基准合约数: {len(df[df['excess_return'] > 0])} / {len(df)} ({len(df[df['excess_return'] > 0])/len(df)*100:.1f}%)")
        print(f"  • 平均交易胜率: {df['win_rate'].mean():6.1%}")
        
        # 风险指标
        print(f"\n⚠️ 风险指标:")
        print(f"  • 平均最大回撤: {df['max_drawdown'].mean():8.2%}")
        print(f"  • 平均夏普比率: {df['sharpe_ratio'].mean():8.2f}")
        
        # 交易统计
        print(f"\n📊 交易统计:")
        print(f"  • 平均交易次数: {df['total_trades'].mean():6.1f}")
        print(f"  • 平均回测天数: {df['total_days'].mean():6.0f}")
        
        # 最佳和最差表现
        best_performer = df.loc[df['strategy_return'].idxmax()]
        worst_performer = df.loc[df['strategy_return'].idxmin()]
        
        print(f"\n🏆 最佳表现:")
        print(f"  • 合约: {best_performer['symbol']}")
        print(f"  • 策略收益率: {best_performer['strategy_return']:8.2%}")
        print(f"  • 超额收益: {best_performer['excess_return']:8.2%}")
        print(f"  • 交易次数: {best_performer['total_trades']:.0f}")
        print(f"  • 胜率: {best_performer['win_rate']:6.1%}")
        
        print(f"\n📉 最差表现:")
        print(f"  • 合约: {worst_performer['symbol']}")
        print(f"  • 策略收益率: {worst_performer['strategy_return']:8.2%}")
        print(f"  • 超额收益: {worst_performer['excess_return']:8.2%}")
        print(f"  • 交易次数: {worst_performer['total_trades']:.0f}")
        print(f"  • 胜率: {worst_performer['win_rate']:6.1%}")
        
        return df
    
    def save_results(self, results, analysis_df):
        """保存回测结果"""
        print(f"\n💾 保存回测结果...")
        
        # 保存汇总结果
        summary_file = os.path.join(self.results_dir, 'backtest_summary.csv')
        analysis_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
        
        # 保存详细结果
        detailed_file = os.path.join(self.results_dir, 'backtest_detailed.json')
        with open(detailed_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存策略配置
        config = {
            'strategy_name': f'MA{self.short_ma}_MA{self.long_ma}_Cross',
            'short_ma': self.short_ma,
            'long_ma': self.long_ma,
            'initial_capital': self.initial_capital,
            'position_size': self.position_size,
            'transaction_cost': self.transaction_cost,
            'backtest_time': datetime.now().isoformat(),
            'total_contracts': len(results),
            'summary_statistics': {
                'avg_strategy_return': float(analysis_df['strategy_return'].mean()),
                'avg_excess_return': float(analysis_df['excess_return'].mean()),
                'win_rate': float(len(analysis_df[analysis_df['strategy_return'] > 0]) / len(analysis_df)),
                'avg_max_drawdown': float(analysis_df['max_drawdown'].mean()),
                'avg_sharpe_ratio': float(analysis_df['sharpe_ratio'].mean())
            }
        }
        
        config_file = os.path.join(self.results_dir, 'backtest_config.json')
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 结果已保存:")
        print(f"  • 汇总结果: {summary_file}")
        print(f"  • 详细结果: {detailed_file}")
        print(f"  • 策略配置: {config_file}")
    
    def run(self):
        """执行完整的回测流程"""
        print("🚀 开始简单策略回测...")
        print("=" * 80)
        
        # 1. 运行回测
        results = self.run_all_backtests()
        
        if not results:
            print("❌ 没有成功的回测结果")
            return False
        
        # 2. 分析结果
        analysis_df = self.analyze_results(results)
        
        # 3. 保存结果
        self.save_results(results, analysis_df)
        
        print(f"\n🎉 简单策略回测完成！")
        print(f"📊 成功回测 {len(results)} 个合约")
        print(f"📁 结果保存在 {self.results_dir} 目录下")
        
        return True

def main():
    """主函数"""
    try:
        backtest = SimpleStrategyBacktest()
        success = backtest.run()
        
        if success:
            print("\n✅ 程序执行成功")
        else:
            print("\n❌ 程序执行失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
    
    print("\n程序结束")

if __name__ == "__main__":
    main()
